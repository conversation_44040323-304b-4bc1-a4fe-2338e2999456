# 用户实例连续性功能实现

## 功能概述

实现了用户在ComfyUI平台上的实例连续性功能。当用户已经在某个ComfyUI实例上执行任务时，新的任务会自动继续在同一个实例上执行，避免频繁切换实例导致的资源浪费和用户体验问题。

## 核心需求

> 工作流在执行选择平台之前先检查当前用户设备是否有任务在运行，如果有且是comfyUI平台，则继续在原本的实例上执行

## 实现原理

### 1. 执行流程

```
用户发起新任务
    ↓
检查用户当前是否有正在运行的ComfyUI任务
    ↓
如果有 → 强制使用ComfyUI平台 + 指定优先实例ID
    ↓
如果没有 → 使用正常的平台选择逻辑
    ↓
实例选择时优先使用指定的实例ID
    ↓
在选定的实例上执行任务
```

### 2. 关键检查点

- **检查时机**: 在工作流执行选择平台之前
- **检查条件**: 用户设备是否有 `pending` 或 `processing` 状态的ComfyUI任务
- **检查范围**: 基于 `deviceToken` 查询用户的任务
- **优先策略**: 选择最新创建的正在运行任务的实例

## 代码实现

### 1. 路由层检查 (`comfyUI.routes.js`)

```javascript
// 查询用户当前是否有正在运行的ComfyUI任务
const runningComfyUITasks = await FlowTask.find({
    deviceToken: deviceToken,
    status: { $in: ['pending', 'processing'] },
    platform: 'comfyui',
    instanceId: { $exists: true, $ne: null }
}).sort({ createdAt: -1 }).limit(1);

// 如果用户已经在ComfyUI平台执行任务，强制使用ComfyUI平台并指定实例
if (runningComfyUITasks.length > 0) {
    const runningTask = runningComfyUITasks[0];
    params.forcePlatform = 'comfyui';
    userContext.preferredInstanceId = runningTask.instanceId;
    userContext.continuityReason = `用户当前正在实例 ${runningTask.instanceId} 上执行任务 ${runningTask.taskId}`;
}
```

### 2. 实例服务优化 (`instanceService.js`)

```javascript
async getAvailableInstance(userId = null, useRunning = false, times = 0, preferredInstanceId = null) {
    // 优先检查用户指定的实例（用于实例连续性）
    if (preferredInstanceId) {
        const preferredInstance = instances.find(instance => instance.instanceId === preferredInstanceId);
        if (preferredInstance && preferredInstance.status === 300) {
            const isFinished = !!await cache.get('instance_finished_' + preferredInstance.appId);
            const isInUse = !!await cache.get('instance_in_use_' + preferredInstance.appId);
            
            // 如果实例正在使用中或空闲，都可以使用
            if (isInUse && !isFinished || isFinished || !isInUse) {
                return preferredInstance;
            }
        }
    }
    // ... 其他选择逻辑
}
```

### 3. 客户端工厂优化 (`ComfyClientFactory.js`)

```javascript
static async createClient(userId = null, preferredInstanceId = null) {
    // 获取可用实例，支持指定优先实例ID
    instance = await instanceService.getAvailableInstance(userId, false, 0, preferredInstanceId);
    // ... 其他逻辑
}
```

### 4. 工作流服务传递 (`WorkflowService.js`)

```javascript
async executeWorkflow(workflowName, inputData, userId, taskId = null, callback, userContext = {}) {
    // 获取优先实例ID
    const preferredInstanceId = userContext.preferredInstanceId || null;
    if (preferredInstanceId) {
        console.log(`用户 ${userId} 指定优先实例: ${preferredInstanceId} (${userContext.continuityReason || '实例连续性'})`);
    }
    
    // 传递给客户端工厂
    const comfyClient = await ComfyClientFactory.createClient(inputData.token, preferredInstanceId);
    // ... 其他逻辑
}
```

## 涉及的文件

### 修改的文件

1. **`server/src/routes/comfyUI.routes.js`**
   - 添加用户当前运行任务检查逻辑
   - 设置强制平台和优先实例ID

2. **`server/src/services/instance/instanceService.js`**
   - 修改 `getAvailableInstance` 方法签名，添加 `preferredInstanceId` 参数
   - 添加优先实例选择逻辑

3. **`server/src/services/comfyClient/ComfyClientFactory.js`**
   - 修改 `createClient` 方法签名，添加 `preferredInstanceId` 参数
   - 传递优先实例ID给实例服务

4. **`server/src/services/comfyClient/service/WorkflowService.js`**
   - 修改 `executeWorkflow` 方法签名，添加 `userContext` 参数
   - 从用户上下文中获取优先实例ID并传递

5. **`server/src/services/unified/UnifiedWorkflowService.js`**
   - 修改 `executeOnComfyUI` 方法签名，添加 `userContext` 参数
   - 传递用户上下文给ComfyUI工作流服务

### 新增的文件

1. **`server/test/user-instance-continuity-test.js`**
   - 功能测试文件，验证各种场景下的实例连续性逻辑

2. **`server/docs/user-instance-continuity.md`**
   - 功能文档，详细说明实现原理和使用方法

## 测试场景

### 场景1: 用户当前没有正在运行的ComfyUI任务
- **预期行为**: 使用正常的平台选择逻辑
- **结果**: ✅ 通过

### 场景2: 用户当前正在ComfyUI平台执行任务
- **预期行为**: 强制使用ComfyUI平台，优先选择当前实例
- **结果**: ✅ 通过

### 场景3: 用户有多个正在运行的ComfyUI任务
- **预期行为**: 选择最新创建的任务的实例
- **结果**: ✅ 通过

### 场景4: 实例选择逻辑验证
- **预期行为**: 优先选择指定的实例ID
- **结果**: ✅ 通过

### 场景5: 完整的工作流程模拟
- **预期行为**: 端到端的实例连续性流程正常工作
- **结果**: ✅ 通过

## 关键特性

### 1. 智能检测
- 基于用户设备令牌 (`deviceToken`) 检测当前运行状态
- 只检查 `pending` 和 `processing` 状态的任务
- 按创建时间倒序排序，选择最新的任务

### 2. 强制平台选择
- 当检测到用户有正在运行的ComfyUI任务时，强制使用ComfyUI平台
- 通过 `params.forcePlatform = 'comfyui'` 实现

### 3. 优先实例选择
- 将用户当前使用的实例ID作为优先选择
- 在实例选择逻辑中优先检查指定实例的可用性
- 支持实例状态验证（运行中、空闲等）

### 4. 向后兼容
- 所有新增参数都是可选的，不影响现有功能
- 当没有指定优先实例时，使用原有的选择逻辑

### 5. 详细日志
- 记录实例连续性的原因和过程
- 便于调试和监控

## 使用效果

1. **用户体验提升**: 用户的任务会在同一个实例上连续执行，避免切换实例的延迟
2. **资源利用优化**: 减少实例间的任务迁移，提高资源利用效率
3. **系统稳定性**: 降低因实例切换导致的任务失败风险
4. **负载均衡**: 在保证连续性的同时，仍然支持负载均衡

## 注意事项

1. **实例可用性**: 如果指定的优先实例不可用，系统会自动回退到正常选择逻辑
2. **任务状态**: 只检查 `pending` 和 `processing` 状态的任务，已完成或失败的任务不影响选择
3. **平台限制**: 只对ComfyUI平台的任务进行连续性检查，不影响其他平台
4. **缓存依赖**: 实例状态检查依赖MongoDB缓存，需要确保缓存服务正常运行

## 总结

成功实现了用户在ComfyUI平台上的实例连续性功能，满足了"工作流在执行选择平台之前先检查当前用户设备是否有任务在运行，如果有且是comfyUI平台，则继续在原本的实例上执行"的需求。该功能通过智能检测、强制平台选择和优先实例选择三个核心机制，确保用户任务的连续性和系统的稳定性。
