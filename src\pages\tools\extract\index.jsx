import React, { useRef, useEffect, useState, useCallback, Suspense } from 'react';
import './index.css';
import { createFlowTask, updateFlowTask, getFlowTasks, getFlowTaskDetail, deleteFlowTask, checkUserBalance } from '../../../api/flowtask';
import { uploadFiles } from '../../../api/ossUpload';
import { executeFlow } from '../../../api/flow';
import { WORKFLOW_NAME } from '../../../data/workflowName';
import PromptIfUnsaved from '../../../components/PromptIfUnsaved';  
import { MdOutlineZoomOutMap, MdOutlineAutoAwesome, MdClose, MdOutlineDocumentScanner } from 'react-icons/md';
import Masonry from 'masonry-layout';
import { Modal, Button, Spin, message, Empty, Tooltip } from 'antd';
import 'antd/dist/reset.css';
import { filterShowcaseByTag } from '../../../config/showcase/showcase';
import { UPLOAD_CONFIG } from '../../../config/uploads/upload';
import { getModelImagePath } from '../../../data/models';
import { generateId, ID_TYPES } from '../../../utils/idGenerator';
import UploadGuideModal from '../../../components/UploadGuideModal';
import ImageInfoModal from '../../../components/ImageInfoModal';
import UploadBox from '../../../components/UploadBox';
import SourceImagePanel from '../../../components/SourceImagePanel';
import TaskPanel from '../../../components/TaskPanel';
import Showcase from '../../../components/Showcase';
import ControlPanel from '../../../components/ControlPanel';
import ResizeHandle from '../../../components/ResizeHandle';
import GenerationArea from '../../../components/GenerationArea';
import ImageExtractionOptions from '../../../components/ImageExtractionOptions';
import RequireLogin from '../../../components/RequireLogin';
import request from '../../../api/request';
import ImageZoomControl from '../../../components/ImageZoomControl';
import ImageNavigator from '../../../components/ImageNavigator';
import ThumbnailList from '../../../components/ThumbnailList';
import { getCurrentUserId } from '../../../api';
import { uploadImage } from '../../../api/upload';
import { 
  getTasks, 
  getTaskById, 
  deleteTask, 
  createTask,
  filterTasksByUser
} from '../../../api/task';
import { showDeleteConfirmModal } from '../../../utils/modalUtils';
import JSZip from 'jszip';
import { getFakeTasksForUser } from '../../../api/task';
import { handleBatchDownload as downloadHelper } from '../../../utils/downloadHelper';
import { getTaskComponent } from '../../../utils/taskAdapters';
import { useTaskContext } from '../../../contexts/TaskContext';

// 添加服务器文件名验证功能
const validateServerFileName = (panel) => {
  if (!panel || !panel.serverFileName || typeof panel.serverFileName !== 'string') {
    console.warn('面板缺少有效的serverFileName属性:', panel);
    return false;
  }
  return true;
};

// 服务器URL构建辅助函数
const buildServerUrl = (resultData, userId) => {
  // 不再进行兼容处理，直接使用serverFileName

  // 获取当前用户ID，避免硬编码
  const currentUserId = userId || getCurrentUserId() || 'developer';
  
  // 基础服务器URL（可从环境变量中获取）
  const baseUrl = process.env.REACT_APP_BACKEND_URL;
  
  // 优先使用服务器返回的相对路径（如果有）
  if (resultData.relativePath) {
    // 使用服务器返回的相对路径构建URL
    return `${baseUrl}${resultData.relativePath}`;
  } else if (resultData.url) {
    // 如果服务器直接返回了完整URL
    return resultData.url;
  } else {
    // 服务器没有返回相对路径或URL，构建基于文件名的URL
    return `${baseUrl}/storage/${currentUserId}/uploads/${resultData.serverFileName}`;
  }
};

// 历史记录功能已移除

const ExtractPage = ({ isLoggedIn, userId }) => {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const containerRef = useRef(null);
  const controlPanelRef = useRef(null);
  const handleRef = useRef(null);
  const [activeTab, setActiveTab] = useState('result');
  const [controlPanelWidth, setControlPanelWidth] = useState(28);
  const showcaseRef = useRef(null);
  const masonryRef = useRef(null);
  const [showUploadGuide, setShowUploadGuide] = useState(false);
  const [uploadGuideType, setUploadGuideType] = useState('source');
  const [isProcessing, setIsProcessing] = useState(false);
  const [sourceImagePanels, setSourceImagePanels] = useState([]);
  const [showSourceUploadGuide, setShowSourceUploadGuide] = useState(false);
  const [operationsPanel, setOperationsPanel] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageDetails, setShowImageDetails] = useState(false);
  const [imageDetailsTask, setImageDetailsTask] = useState(null);
  const [showAdvancedText, setShowAdvancedText] = useState(false);

  
  // 添加拖动状态管理
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const lastPosition = useRef({ x: 0, y: 0 });
  
  // 添加图片缩放相关状态
  const [imageScale, setImageScale] = useState(100);
  const [initialScale, setInitialScale] = useState(100);
  const imageRef = useRef(null);
  
  // 添加加载状态
  const [isLoadingTasks, setIsLoadingTasks] = useState(false);
  
  // 添加提取选项状态
  const [extractionOptions, setExtractionOptions] = useState({
    optionValue: 1, // 默认选择"服装"，对应数字1
    customText: '' // 自定义文本
  });
  
  // 任务列表状态
  const [generationTasks, setGenerationTasks] = useState([]);
  
  // 页面刷新/关闭提示
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）';
        return e.returnValue;
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);
  
  const generationAreaRef = useRef(null);
  // 初始化和清理Masonry布局
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      // 销毁已存在的实例
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }

      // 创建新实例
      masonryRef.current = new Masonry(showcaseRef.current, {
        itemSelector: '.showcase-item',
        columnWidth: '.showcase-item',
        percentPosition: true,
        transitionDuration: '0.3s',
        initLayout: true,
        gutter: 16,
        fitWidth: false,
        horizontalOrder: true
      });

      // 监听窗口大小变化
      const handleResize = () => {
        if (masonryRef.current) {
          setTimeout(() => {
            masonryRef.current.layout();
          }, 100);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (masonryRef.current) {
          masonryRef.current.destroy();
          masonryRef.current = null;
        }
      };
    }
  }, [activeTab]);

  // 监听图片加载
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      const images = showcaseRef.current.getElementsByTagName('img');
      let loadedCount = 0;

      const handleImageLoad = () => {
        loadedCount++;
        if (masonryRef.current) {
          masonryRef.current.layout();
        }
      };

      Array.from(images).forEach(img => {
        if (img.complete) {
          handleImageLoad();
        } else {
          img.addEventListener('load', handleImageLoad);
        }
      });

      return () => {
        Array.from(images).forEach(img => {
          img.removeEventListener('load', handleImageLoad);
        });
      };
    }
  }, [activeTab]);

  // 处理原始图片上传结果
  const handleSourceUploadResult = (results) => {
    setHasUnsavedChanges(true);
    console.log('原始图片上传结果:', results);
    
    try {
      if (results.type === 'panels') {
        // 如果是新上传，添加新面板
        const panelsWithType = results.panels.map(panel => ({
          ...panel,
          type: 'source',
          source: panel.source || 'upload', // 添加source属性，标记为用户上传
          file: panel.file // 确保保留原始文件对象
        }));
        setSourceImagePanels(prevPanels => [...prevPanels, ...panelsWithType]);
        
        // 移除对显示提示的设置
        // setShowTips(true);
      } else if (results.type === 'update') {
        // 处理面板更新（上传完成或失败的回调）
        console.log('收到面板更新:', results.panel);
        
        if (results.panel) {
          const updatedPanel = results.panel;
          
          // 检查是否包含服务器处理完成的标志
          const isUploadCompleted = updatedPanel.serverFileName || 
                                   (updatedPanel.processInfo && Object.keys(updatedPanel.processInfo).length > 0);
          
          // 强制设置完成状态的标志
          const forceCompleted = isUploadCompleted && updatedPanel.status === 'uploading';
          
          if (forceCompleted) {
            console.log('检测到上传已完成但状态未更新，强制设置为completed状态');
          }
          
          // 更新对应ID的面板
          setSourceImagePanels(prevPanels => 
            prevPanels.map(panel => {
              if (panel.componentId === updatedPanel.componentId) {
                console.log(`更新面板 ${panel.componentId}:`);
                console.log('- 原始状态:', panel.status);
                console.log('- 服务器返回状态:', updatedPanel.status);
                console.log('- 最终状态:', forceCompleted ? 'completed' : (updatedPanel.status || 'completed'));
                
                // 返回更新后的面板，确保保留所有重要字段
                return {
                  ...panel,
                  // 如果检测到上传完成标志但状态仍为uploading，则强制设为completed
                  status: forceCompleted ? 'completed' : (updatedPanel.status || 'completed'),
                  errorMessage: updatedPanel.errorMessage,
                  processedFile: updatedPanel.processedFile,
                  processedUrl: updatedPanel.processedUrl,
                  processInfo: updatedPanel.processInfo,
                  serverFileName: updatedPanel.serverFileName
                };
              }
              return panel;
            })
          );
        }
      } else if (results.type === 'error') {
        console.error('上传错误:', results.error);
        message.error('上传失败: ' + results.error);
        // 移除处理中的面板
        setSourceImagePanels(prevPanels => 
          prevPanels.filter(panel => panel.status !== 'processing'));
      }
    } catch (error) {
      console.error('上传结果处理失败:', error);
      message.error('上传结果处理失败: ' + (error.message || '未知错误'));
      // 移除处理中的面板
      setSourceImagePanels(prevPanels => 
        prevPanels.filter(panel => panel.status !== 'processing'));
    }
  };

  // 处理原始图片上传
  const handleSourceFileUpload = (file) => {
    console.log('原始图片上传:', file);
    
    // 验证文件类型和大小
    if (!UPLOAD_CONFIG.isValidFileType(file.name)) {
      message.error('不支持的文件类型');
      return;
    }
    
    if (!UPLOAD_CONFIG.isValidFileSize(file.size)) {
      message.warning(`文件需${UPLOAD_CONFIG.maxSize}MB以内`);
      return;
    }
    
    // 创建一个临时URL用于预览
    const fileUrl = URL.createObjectURL(file);
    
    // 生成唯一ID
    const imageId = generateId(ID_TYPES.COMPONENT);
    
    // 创建面板数据
    const panel = {
      componentId: imageId,
      title: '原始图片',
      status: 'completed', // 直接设置为完成状态，因为不立即上传
      serverFileName: file.name,
      url: fileUrl, // 使用本地URL
      fileInfo: {
        name: file.name,
        size: file.size,
        type: file.type,
        serverFileName: file.name
      },
      type: 'source',
      source: 'upload', // 设置来源为上传
      file: file // 保存原始文件对象，供后续上传使用
    };
    
    // 通知创建面板
    handleSourceUploadResult({ type: 'panels', panels: [panel] });
    
    // 显示上传成功消息
    message.success('图片上传成功');
  };

  // 处理删除原始图片面板
  const handleDeleteSourceImagePanel = (panelId) => {
    setHasUnsavedChanges(true);
    setSourceImagePanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
  };

  // 处理重新上传原始图片
  const handleReuploadSource = (panel) => {
    if (panel && panel.componentId) {
      // 第一步：删除当前面板
      handleDeleteSourceImagePanel(panel.componentId);
      
      // 第二步：延迟一点点时间后触发上传区域点击（确保UI已更新）
      setTimeout(() => {
        // 如果已经没有面板了，上传框应该会显示出来，直接触发其点击事件
        const uploadBox = document.getElementById('source-upload-box');
        if (uploadBox) {
          uploadBox.click();
        }
      }, 50);
    }
  };

  // 处理原始图片状态变化
  const handleSourceImageStatusChange = (panelId, newStatus) => {
    setSourceImagePanels(prevPanels =>
      prevPanels.map(panel =>
        panel.componentId === panelId ? { ...panel, status: newStatus } : panel
      )
    );
  };

  // 添加TaskContext的updateTask函数
  const { updateTask } = useTaskContext();
  
  // 处理开始生成按钮点击
  const handleGenerate = async () => {
    console.log('开始生成...');
    setIsProcessing(true)
    let taskData = null;
    try {
      // 检查是否有原始图片面板
      if (sourceImagePanels.length === 0) {
        message.error('请先上传原始图片');
        setIsProcessing(false);
        return;
      }
      // 获取当前用户ID
      const currentUserId = userId || getCurrentUserId() || 'developer';

      // 处理可能的自定义上传图片
      let sourceImageToUse = sourceImagePanels[0];
      const balance = await checkUserBalance('图片取词', 'extract', 1);
      if(balance.code !== 200){
        message.error(balance.message);
setIsProcessing(false);
        return;
      }
      // 验证sourceImagePanels[0]是否有有效的serverFileName
      if (!validateServerFileName(sourceImageToUse)) {
        console.error('错误：源图片缺少有效的serverFileName，无法创建任务');
        message.error('源图片缺少有效的文件名信息，无法创建任务');
        setIsProcessing(false);
        return;
      }
      // 检查是否有需要上传的图片（判断是否有file属性和source为'upload'）
      if (sourceImageToUse.file && sourceImageToUse.source === 'upload') {
        // 显示上传中提示
        message.loading('正在上传图片...', 0);
        
        try {
          // 将文件上传到服务器
          const uploadResult = await uploadFiles([sourceImageToUse.file],"extract");
          if (uploadResult) {
            const resultData = uploadResult.fileInfos[0];
            sourceImageToUse = {
              ...sourceImageToUse,
              image: resultData.url, // 使用构建的服务器URL
              url: resultData.url, // 同时设置url属性以保持一致性
              source: 'history',
              fileInfo: {
                ...(sourceImageToUse.fileInfo || {}),
                ...resultData
              }
            };
          }
        } catch (error) {
          console.error('上传图片时出错:', error);
          message.error('图片上传失败: ' + (error.message || '未知错误'));
          setIsProcessing(false);
          return;
        } finally {
          // 关闭上传中提示
          message.destroy();
        }
      }

      // 创建一个新的任务ID
      const taskId = generateId(ID_TYPES.TASK);
      
      // 创建任务数据对象
      taskData = {
        taskId: taskId,
        userId: currentUserId, // 确保包含用户ID
        createdAt: new Date(),
        status: 'processing',
        imageCount: 1, // 固定为1，因为只需要一个文本描述结果
        taskType: 'extract', // 指定任务类型为图片取词
        pageType: 'extract', // 指定页面类型
        requiresImage: true, // 明确需要图片
        // 使用新的组件数组结构
        extractedText:"",
        components: [
          {
            componentType: 'sourceImagePanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            title: '原始图片',
            type: 'source',
            originalImage: sourceImageToUse.url,
            url: sourceImageToUse.url,
            source: sourceImageToUse.source || 'upload',
            serverFileName: sourceImageToUse.serverFileName,
            fileInfo: {
              ...(sourceImageToUse.fileInfo || {}),
              serverFileName: sourceImageToUse.serverFileName
            }
          },
          {
            componentType: 'imageExtractionOptions',
            componentId: generateId(ID_TYPES.COMPONENT),
            optionValue: extractionOptions.optionValue, // 使用数字值1、2、3或4
            customText: extractionOptions.customText || "", // 保存自定义文本
            extractText: true
            // 移除 extractionDescription，只使用数字值传递给后端
          }
        ],
        processInfo:{
          results:[]
        }
      };
      
      // 记录任务信息到控制台
      console.log('创建图片取词任务:', {
        taskId,
        components: taskData.components,
        optionValue: extractionOptions.optionValue // 只记录数字选项，不包含描述文本
      });
      
      setIsProcessing(true);
      
      // 先添加到本地状态，使UI立即响应
      if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      await createFlowTask(taskData);
      const {promptId,instanceId,status,url} = await executeFlow(WORKFLOW_NAME.EXTRACT,{
        "38": {
          "select": extractionOptions.optionValue
        },
        "33": {
          "url": sourceImageToUse.url
        },
        "32": {
          "filename_suffix": taskId,
          "filename_number_padding": 0
        },
        "taskId":taskId,
        "subInfo":{
          "type": "extract",
          "title":"图片取词",
          "customText": extractionOptions.customText || "",
          "count":1
        }
      },taskData.taskId);
      setIsProcessing(false);
      setHasUnsavedChanges(false);
      if( generationAreaRef.current){
        taskData.promptId = promptId;
        taskData.instanceId = instanceId;
        taskData.url = url;
        taskData.newTask = true;
        generationAreaRef.current.setGenerationTasks(taskData);
    }
    } catch (error) {
      console.error('创建任务失败:', error);
      message.error('创建任务失败: ' + error.message);
      taskData.status = 'failed';
      setIsProcessing(false);
      setHasUnsavedChanges(false);
      setIsProcessing(false);      
      if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
    }
  };

  // 处理单张图片下载
  const handleDownloadImage = async (imageUrl, taskId, index) => {
    try {
      message.info('准备下载...');
      const httpsUrl = imageUrl.replace(/^http:/, 'https:');
      const response = await fetch(httpsUrl);
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `${taskId}_${parseInt(index) + 1}.jpg`;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl);
    } catch (error) {
      message.error('下载失败');
      console.error('下载出错:', error);
    }
  };

  // 处理批量下载
  const handleBatchDownload = async (task) => {
    // 调用通用下载辅助函数
    await downloadHelper(
      task, 
      fetch, // 传递fetch函数
      handleDownloadImage // 传递单张图片下载函数
    );
  };

  // 处理删除任务
  const handleDeleteTask = (taskId, skipConfirm = false) => {
    // 定义删除任务的函数
    const deleteTaskFunction = async () => {
      try {
        // 获取当前用户ID，如果未登录则使用开发者ID
        const userId = getCurrentUserId() || 'developer'; 
        
        // 显示操作中的消息
        message.loading({ content: '正在删除...', key: `delete-${taskId}` });
        
        // 先从本地状态中移除，提供即时反馈
        setGenerationTasks(prev => prev.filter(task => task.taskId !== taskId));
        
        // 调用API进行删除
        const success = await deleteTask(taskId, userId);
        
        if (success) {
          message.success({ content: '记录已删除', key: `delete-${taskId}` });
        } else {
          // 如果API返回失败但UI已更新，只在控制台记录警告
          console.warn('API删除任务可能失败，但前端已更新状态');
        }
      } catch (error) {
        console.error('删除任务失败:', error);
        message.error({ 
          content: '删除任务失败: ' + error.message,
          key: `delete-${taskId}`
        });
        
        // 即使API调用失败，仍从本地状态中移除
        setGenerationTasks(prev => prev.filter(task => task.taskId !== taskId));
      }
    };
    
    // 如果跳过确认，直接删除
    if (skipConfirm) {
      deleteTaskFunction();
      return;
    }
    
    // 否则显示确认对话框
    showDeleteConfirmModal({
      title: '确认删除',
      content: '确定要删除这条生成记录吗？删除后将无法恢复。',
      okText: '确认删除',
      cancelText: '取消',
      okButtonProps: {
        danger: true,
      },
      animation: false,
      transitionName: '',
      maskTransitionName: '',
      onOk: deleteTaskFunction
    });
  };

  // 处理编辑任务
  const handleEditTask = (task) => {
    if (!task) return;
    
    try {
      // 控制台记录任务数据
      console.log('回填任务数据:', task);
      
      // 只处理数组格式的组件，不再提供兼容性处理
      if (!Array.isArray(task.components)) {
        console.warn('任务数据格式不正确，components应为数组');
        return;
      }
      console.log('处理的组件数据:', task.components);
      
      // 直接从组件数组中查找相应组件 - 使用标准小写组件名称
      const sourceComponent = task.components.find(c => c.componentType === 'sourceImagePanel');
      const extractionOptionsComponent = task.components.find(c => c.componentType === 'imageExtractionOptions');
      
      console.log('获取到的编辑组件:', {
        sourceComponent,
        extractionOptionsComponent
      });
      
      // 恢复原始图片面板
      if (sourceComponent) {
        setSourceImagePanels([
          {
            ...sourceComponent,
            status: 'completed'
          }
        ]);
        console.log('回填原始图片:', sourceComponent);
      } else {
        console.warn('未找到原始图片组件(sourceImagePanel)，请检查任务数据');
      }
      
      // 恢复提取选项设置
      if (extractionOptionsComponent) {
        setExtractionOptions({
          optionValue: extractionOptionsComponent.optionValue || 1,
          customText: extractionOptionsComponent.customText || ''
        });
        console.log('回填提取选项:', extractionOptionsComponent);
      } else {
        console.warn('未找到提取选项组件(imageExtractionOptions)，使用默认设置');
        setExtractionOptions({
          optionValue: 1,
          customText: ''
        });
      }
      
      // 切换到结果标签页
      setActiveTab('result');
      
      message.success({
        content: '配置已重新导入，可继续进行调整',
        duration: 5
      });
      
      console.log('重新编辑任务完成:', {
        sourceImagePanel: sourceComponent,
        activeTab: 'result'
      });
    } catch (error) {
      console.error('处理编辑任务时出错:', error);
      message.error('恢复任务设置失败: ' + (error.message || '未知错误'));
    }
  };

  // 添加点击外部关闭下拉菜单的处理
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdowns = document.querySelectorAll('.dropdown-menu');
      dropdowns.forEach(dropdown => {
        if (!dropdown.parentElement.contains(event.target)) {
          dropdown.classList.remove('show');
        }
      });
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const handleViewDetails = async (image, task) => {
    try {
      console.log('查看图片详情:', image);
      console.log('相关任务数据:', task);
      
      // 只处理数组格式的组件，不再提供兼容性处理
      if (!Array.isArray(task.components)) {
        console.warn('任务数据格式不正确，components应为数组');
        return;
      }
      console.log('处理的组件数据:', task.components);
      
      // 预加载图片
      const preloadImage = async (url) => {
        return new Promise((resolve, reject) => {
          if (!url) {
            resolve(null);
            return;
          }
          
          console.log('预加载图片:', url);
          const img = new Image();
          img.onload = () => resolve(img);
          img.onerror = (err) => {
            console.warn('图片预加载失败:', url, err);
            resolve(null); // 失败不阻止后续操作
          };
          img.src = url;
        });
      };
      
      // 直接获取各组件
      const sourceComponent = task.components.find(c => c.componentType === 'sourceImagePanel');
      const extractionOptionsComponent = task.components.find(c => c.componentType === 'imageExtractionOptions');
      
      console.log('获取到的详情组件:', {
        sourceComponent,
        extractionOptionsComponent
      });
      
      // 预加载源图和结果图
      if (sourceComponent?.originalImage || sourceComponent?.url) {
        await preloadImage(sourceComponent?.originalImage || sourceComponent?.url);
      }
      
      if (image.url) {
        await preloadImage(image.url);
      }
      
      // 准备规范化的组件数据，使用数组格式
      const adaptedComponents = [];
      
      // 添加源图片组件
      if (sourceComponent) {
        adaptedComponents.push({
          ...sourceComponent,
          componentType: 'sourceImagePanel',
          componentId: generateId(ID_TYPES.COMPONENT),
          title: sourceComponent.title || '原始图片',
          type: sourceComponent.type || 'source'
        });
      } else {
        // 如果缺少源图片组件，创建一个默认的
        adaptedComponents.push({
          componentType: 'sourceImagePanel',
          componentId: generateId(ID_TYPES.COMPONENT),
          title: '原始图片',
          type: 'source',
          url: '',
          originalImage: '',
          fileInfo: {
            width: image.fileInfo?.width || 800,
            height: image.fileInfo?.height || 1200,
            format: 'image/jpeg'
          }
        });
      }
      
      // 添加提取选项组件
      if (extractionOptionsComponent) {
        adaptedComponents.push({
          ...extractionOptionsComponent,
          componentType: 'imageExtractionOptions',
          componentId: generateId(ID_TYPES.COMPONENT),
          extractText: extractionOptionsComponent.extractText !== undefined ? extractionOptionsComponent.extractText : true,
          extractElements: extractionOptionsComponent.extractElements !== undefined ? extractionOptionsComponent.extractElements : false
        });
      } else {
        // 如果缺少提取选项组件，创建一个默认的
        adaptedComponents.push({
          componentType: 'imageExtractionOptions',
          componentId: generateId(ID_TYPES.COMPONENT),
          extractText: true,
          extractElements: false
        });
      }
      
      console.log('为详情模态框准备的组件数据:', adaptedComponents);
      
      // 设置基础数据
      setSelectedImage({
        ...image,
        taskId: task.taskId,
        createdAt: task.createdAt,
        components: adaptedComponents,
        imageIndex: image.imageIndex || 0,
        seed: Number(task.seed || 0)
      });
      
      // 设置任务信息
      setImageDetailsTask({
        ...task,
        components: adaptedComponents // 使用规范化的组件数据
      });
      
      // 打开弹窗
      setShowImageDetails(true);
      
      // 在下一个微任务中设置其他初始状态
      queueMicrotask(() => {
        // 初始化图片位置和缩放状态
        setImagePosition({ x: 0, y: 0 });
        setIsDragging(false);
        lastPosition.current = { x: 0, y: 0 };
        setImageScale(100);
        setInitialScale(100);
        

        
        // 初始化文本弹窗状态
        setShowAdvancedText(false);
      });
    } catch (error) {
      console.error('处理查看图片详情时出错:', error);
      message.error('无法加载图片详情');
    }
  };

  // 添加关闭弹窗时的处理函数
  const handleCloseImageDetails = () => {
    // 直接关闭弹窗，不使用动画
    setShowImageDetails(false);
    
    // 重置状态，无需延迟
    setSelectedImage(null);
    // 重置任务信息，避免保留旧任务导致重新打开时出错
    setImageDetailsTask(null);
    setImagePosition({ x: 0, y: 0 });
    setIsDragging(false);
    lastPosition.current = { x: 0, y: 0 };
    setImageScale(100);
    setInitialScale(100);
    

    
    // 重置文本弹窗状态
    setShowAdvancedText(false);
  };

  // 添加点击外部关闭弹出层的处理函数
  useEffect(() => {
    const handleClickOutside = (event) => {
      // 检查点击是否发生在文本按钮上
      const isTextButton = event.target.closest('.text-button');
      // 检查点击是否发生在弹窗内部
      const isInsidePopup = event.target.closest('.text-popup');
      
      // 如果点击既不是文本按钮也不是弹窗内部，则关闭所有弹窗
      if (!isTextButton && !isInsidePopup) {
        setShowAdvancedText(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 计算初始缩放比例
  const calculateInitialScale = (img) => {
    if (!img) return 100;
    const container = img.parentElement;
    if (!container) return 100;
    
    // 计算图片在容器中的实际显示尺寸与真实尺寸的比例
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const imageNaturalWidth = img.naturalWidth;
    const imageNaturalHeight = img.naturalHeight;
    
    // 计算图片适应容器时的尺寸
    const containerRatio = containerWidth / containerHeight;
    const imageRatio = imageNaturalWidth / imageNaturalHeight;
    
    let scale;
    if (imageRatio > containerRatio) {
      // 图片较宽，以容器宽度为基准
      scale = (containerWidth / imageNaturalWidth) * 100;
    } else {
      // 图片较高，以容器高度为基准
      scale = (containerHeight / imageNaturalHeight) * 100;
    }

    // 如果计算出的缩放比例大于100%，说明图片实际尺寸小于容器
    // 这种情况下我们应该将图片显示为其实际大小
    if (scale > 100) {
      scale = 100;
    }
    
    // 返回相对于真实尺寸的缩放百分比，四舍五入到整数
    return Math.round(scale);
  };

  // 处理图片加载完成
  const handleImageLoad = (e) => {
    const img = e.target;
    const initialScaleValue = calculateInitialScale(img);
    setInitialScale(initialScaleValue);  // 设置初始比例
    setImageScale(initialScaleValue);    // 设置当前比例
    imageRef.current = img;
  };

  // 处理缩放变化
  const handleScaleChange = (newScale) => {
    setImageScale(newScale);
  };

  // 处理重置图片位置和缩放
  const handleReset = () => {
    setImageScale(initialScale);
    setImagePosition({ x: 0, y: 0 });
    lastPosition.current = { x: 0, y: 0 };
  };

  // 处理图片上传
  const handleUpload = async (file, fileList) => {
    // ... existing code ...
  };

  // 检查任务状态
  const checkTaskStatus = async (taskId) => {
    try {
      const task = await getTaskById(taskId);
      
      if (task) {
        console.log('任务状态:', task.status);
        
        // 根据任务状态更新UI
        if (task.status === 'completed') {
          // 任务完成，获取生成的图片
          setGenerationTasks(prevTasks => 
            prevTasks.map(t => 
              t.taskId === taskId ? task : t
            )
          );
          
          // 调用updateTask以触发提示音
          updateTask(task);
          
          setIsProcessing(false);
          message.success('文本生成完成');
        } else if (task.status === 'failed') {
          // 任务失败
          setIsProcessing(false);
          message.error('图片取词失败: ' + (task.errorMessage || '未知错误'));
          
          // 更新任务状态
          const failedTask = { ...task, status: 'failed', errorMessage: task.errorMessage };
          setGenerationTasks(prevTasks => 
            prevTasks.map(t => 
              t.taskId === taskId ? failedTask : t
            )
          );
          
          // 调用updateTask以触发失败提示音
          updateTask(failedTask);
        } else {
          // 任务仍在处理中，继续轮询
          setTimeout(() => checkTaskStatus(taskId), 2000); // 每2秒检查一次
        }
      } else {
        setIsProcessing(false);
        message.error('获取任务状态失败: 任务不存在');
        
        // 修正：补充task变量定义
        const task = generationTasks.find(t => t.taskId === taskId) || {};
        // 更新任务状态为失败
        const failedTask = { ...task, status: 'failed', errorMessage: '任务不存在' };
        setGenerationTasks(prevTasks => 
          prevTasks.map(t => 
            t.taskId === taskId ? failedTask : t
          )
        );
        
        // 调用updateTask以触发失败提示音
        updateTask(failedTask);
      }
    } catch (error) {
      console.error('获取任务状态失败:', error);
      setIsProcessing(false);
      message.error('获取任务状态失败: ' + (error.message || '未知错误'));
      
      // 修正：补充task变量定义
      const task = generationTasks.find(t => t.taskId === taskId) || {};
      // 更新任务状态为失败
      const failedTask = { ...task, status: 'failed', errorMessage: error.message || '未知错误' };
      setGenerationTasks(prevTasks => 
        prevTasks.map(t => 
          t.taskId === taskId ? failedTask : t
        )
      );
      
      // 调用updateTask以触发失败提示音
      updateTask(failedTask);
    }
  };

  const [currentTaskId, setCurrentTaskId] = useState(null);

  // 处理提取选项变更
  const handleExtractionOptionsChange = (newOptions) => {
    setExtractionOptions(newOptions);
    setHasUnsavedChanges(true);
    console.log('提取选项已更新:', newOptions); // 包含 optionValue 和 customText 字段
  };

  // 使用RequireLogin组件包装整个页面内容
  return (
    <RequireLogin isLoggedIn={isLoggedIn} featureName="图片取词功能">
      <PromptIfUnsaved when={hasUnsavedChanges} message="操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）" />
      <div className="extract-page">
        <div className="extract-container" ref={containerRef}>
          <ControlPanel
            ref={controlPanelRef}
            width={`${controlPanelWidth}%`}
            onGenerate={handleGenerate}
            disabled={isProcessing}
            featureName="extract"
            quantity={1}
          >
                    {/* 原始图片上传区域或原始图片面板 - 位于最上方 */}
                    {sourceImagePanels.length === 0 ? (
                      <UploadBox
                        id="source-upload-box"
                        onUpload={handleSourceFileUpload}
                        onShowGuide={() => {
                          setUploadGuideType('source');
                          setShowSourceUploadGuide(true);
                        }}
                        onChange={handleSourceUploadResult}
                        onUploadResult={handleSourceUploadResult}
                        panels={sourceImagePanels}
                        className="mt-2"
                        showSupportTag={false}
                        pageType="extract"
                        uploadType="source"
                      />
                    ) : (
                      // 展示原始图片面板
                      sourceImagePanels.map((panel) => (
                        <SourceImagePanel
                          key={panel.componentId}
                          panel={panel}
                          onExpandClick={(panel, position) => {
                            // 创建增强的面板对象，避免警告
                            const enhancedPanel = {
                              ...panel,
                              isEnhancedSourceImagePanel: true,
                              processedFile: panel.processedFile || panel.preview || panel.url || panel.image,
                              fileInfo: panel.fileInfo || {
                                size: 500000,
                                format: 'image/jpeg',
                                type: 'image/jpeg',
                                width: 800,
                                height: 1200
                              }
                            };
                            
                            setOperationsPanel({
                              panel: enhancedPanel,
                              position
                            });
                          }}
                          onDelete={() => handleDeleteSourceImagePanel(panel.componentId)}
                          onReupload={() => handleReuploadSource(panel)}
                          onStatusChange={(newStatus) => handleSourceImageStatusChange(panel.componentId, newStatus)}
                          isActive={panel.status === 'completed'}
                          onPanelsChange={setSourceImagePanels}
                          pageType="extract"
                        />
                      ))
                    )}
                    
                    {/* 图片取词选项组件 */}
                    <ImageExtractionOptions
                      onChange={handleExtractionOptionsChange}
                      defaultOptions={extractionOptions}
                    />
          </ControlPanel>

          <ResizeHandle
            ref={handleRef}
            containerRef={containerRef}
            onResize={setControlPanelWidth}
            minWidth={25}
            maxWidth={50}
          />

                  <GenerationArea
            ref={generationAreaRef}    setIsProcessing={setIsProcessing}

            activeTab={activeTab}
            onTabChange={setActiveTab}
            tasks={Array.isArray(generationTasks) ? generationTasks : []}
            onEditTask={handleEditTask}
            onDownloadImage={handleDownloadImage}
            onBatchDownload={handleBatchDownload}
            onDeleteTask={handleDeleteTask}
            pageType="extract"
          />
        </div>

        {/* 上传指南模态框 */}
        {showUploadGuide && (
          <UploadGuideModal
            type={uploadGuideType}
            pageType="extract"
            onClose={() => setShowUploadGuide(false)}
            onUpload={(result) => {
              console.log('收到上传结果:', result);
              // 根据结果中的shouldClose字段决定是否关闭弹窗
              if (result.shouldClose !== false) {
                setShowUploadGuide(false);
              }
            }}
          />
        )}

        {/* 原始图片上传指导弹窗 */}
        {showSourceUploadGuide && (
          <UploadGuideModal
            type="source"
            pageType="extract"
            onClose={() => setShowSourceUploadGuide(false)}
            onUpload={(result) => {
              console.log('收到原始图片上传结果:', result);
              
              // 确保结果中的面板都有正确的source和file属性
              if (result.type === 'panels' && Array.isArray(result.panels)) {
                result.panels = result.panels.map(panel => ({
                  ...panel,
                  source: panel.source || 'upload' // 设置来源为上传
                }));
              }
              
              handleSourceUploadResult(result);
              
              // 根据结果中的shouldClose字段决定是否关闭弹窗
              if (result.shouldClose !== false) {
                setShowSourceUploadGuide(false);
              }
            }}
          />
        )}

        {/* 添加操作弹窗 */}
        {operationsPanel && (
          <ImageInfoModal
            panel={operationsPanel.panel}
            position={operationsPanel.position}
            onClose={() => setOperationsPanel(null)}
            onDelete={() => handleDeleteSourceImagePanel(operationsPanel.panel.componentId)}
            onReupload={() => handleReuploadSource(operationsPanel.panel)}
            pageType="extract"
          />
        )}

        {/* 显示生成中的加载状态 */}
        {isProcessing && (
          <div className="generating-status">
            <Spin size="large" />
            <p>正在生成中，请稍候...</p>
          </div>
        )}
      </div>
    </RequireLogin>
  );
};

export default ExtractPage; 