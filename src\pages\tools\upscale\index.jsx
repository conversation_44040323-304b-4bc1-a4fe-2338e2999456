import React, { useRef, useEffect, useState, useCallback, Suspense } from 'react';
import { checkUserBalance } from '../../../api/flowtask';
import { createFlowTask, updateFlowTask, getFlowTasks, getFlowTaskDetail, deleteFlowTask } from '../../../api/flowtask';
import { uploadFiles } from '../../../api/ossUpload';
import { executeFlow } from '../../../api/flow';
import { WORKFLOW_NAME } from '../../../data/workflowName';
import './index.css';
import { MdOutlineZoomOutMap, MdOutlineAutoAwesome, MdClose, MdOutlineDescription } from 'react-icons/md';
import Masonry from 'masonry-layout';
import { Modal, Button, Spin, message, Empty, Tooltip } from 'antd';
import 'antd/dist/reset.css';
import { filterShowcaseByTag } from '../../../config/showcase/showcase';
import { UPLOAD_CONFIG } from '../../../config/uploads/upload';
import { getModelImagePath } from '../../../data/models';
import { generateId, ID_TYPES } from '../../../utils/idGenerator';
import UploadGuideModal from '../../../components/UploadGuideModal';
import ImageInfoModal from '../../../components/ImageInfoModal';
import ImageDetailsModal from '../../../components/ImageDetailsModal';
import UploadBox from '../../../components/UploadBox';
import SourceImagePanel from '../../../components/SourceImagePanel';
import TaskPanel from '../../../components/TaskPanel';
import Showcase from '../../../components/Showcase';
import ControlPanel from '../../../components/ControlPanel';
import ResizeHandle from '../../../components/ResizeHandle';
import GenerationArea from '../../../components/GenerationArea';
import RequireLogin from '../../../components/RequireLogin';
import request from '../../../api/request';
import ImageZoomControl from '../../../components/ImageZoomControl';
import ImageNavigator from '../../../components/ImageNavigator';
import ThumbnailList from '../../../components/ThumbnailList';
import { getCurrentUserId } from '../../../api';
import { uploadImage } from '../../../api/upload';
import PromptIfUnsaved from '../../../components/PromptIfUnsaved';
import { 
  getTasks, 
  getTaskById, 
  deleteTask, 
  createTask, 
  filterTasksByUser,
  getFakeTasksForUser
} from '../../../api/task';
import { showDeleteConfirmModal } from '../../../utils/modalUtils';
import JSZip from 'jszip';
import MagnificationSize from '../../../components/MagnificationSize';
import MagnificationSizeModal from '../../../components/MagnificationSizeModal';
import { handleBatchDownload as downloadHelper } from '../../../utils/downloadHelper';
import { getTaskComponent } from '../../../utils/taskAdapters';
import { useTaskContext } from '../../../contexts/TaskContext';

const MemoizedImageDetailsModal = React.memo(ImageDetailsModal);

// 在文件顶部添加任务请求缓存对象
const taskRequestCache = {};

// 适配任务数据格式，确保关键字段存在
const adaptTaskFormat = (task) => {
  if (!task) return null;
  
  // 只使用标准taskId字段
  const taskId = task.taskId;
  
  // 确保组件是数组
  const components = Array.isArray(task.components) ? task.components : [];
  
  return {
    ...task,
    taskId: taskId, // 保留taskId
    components: components, // 确保components是数组
    generatedImages: task.generatedImages || [], // 确保生成图片数组存在
    status: task.status || 'completed', // 确保状态字段存在
    taskType: task.taskType || 'upscale', // 添加任务类型
    pageType: task.pageType || 'upscale' // 添加页面类型
  };
};

const UpscalePage = ({ isLoggedIn, userId }) => {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const containerRef = useRef(null);
  const controlPanelRef = useRef(null);
  const generationAreaRef = useRef(null);
  const handleRef = useRef(null);
  const [activeTab, setActiveTab] = useState('result');
  const [controlPanelWidth, setControlPanelWidth] = useState(28);
  const showcaseRef = useRef(null);
  const masonryRef = useRef(null);
  const [showUploadGuide, setShowUploadGuide] = useState(false);
  const [uploadGuideType, setUploadGuideType] = useState('source');
  const [isProcessing, setIsProcessing] = useState(false);
  const [sourceImagePanels, setSourceImagePanels] = useState([]);
  const [currentReuploadSourceImagePanelId, setCurrentReuploadSourceImagePanelId] = useState(null);
  const [showSourceUploadGuide, setShowSourceUploadGuide] = useState(false);
  const [operationsPanel, setOperationsPanel] = useState(null);
  const [showImageInfo, setShowImageInfo] = useState(false);
  const [currentImagePanel, setCurrentImagePanel] = useState(null);
  const [imageQuantity] = useState(1); // 固定生成图片数量为1张，移除setter
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageDetails, setShowImageDetails] = useState(false);
  const [imageDetailsTask, setImageDetailsTask] = useState(null);
  const [showAdvancedText, setShowAdvancedText] = useState(false);

  const [advancedPopupPosition, setAdvancedPopupPosition] = useState({ top: 0, left: 0 });
  
  // 添加拖动状态管理
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const lastPosition = useRef({ x: 0, y: 0 });
  
  // 添加图片缩放相关状态
  const [imageScale, setImageScale] = useState(100);
  const [initialScale, setInitialScale] = useState(100);
  const imageRef = useRef(null);
  
  // 添加加载状态
  const [isLoadingTasks, setIsLoadingTasks] = useState(false);
  
  // 尺寸选择状态
  const [showSizeSettingsModal, setShowSizeSettingsModal] = useState(false);
  const [sizeSettings, setSizeSettings] = useState({
    scale: 2,
    width: 1024,
    height: 1024
  });
  // 添加一个新的状态来保存用户确认过的设置
  const [savedSizeSettings, setSavedSizeSettings] = useState(null);
  const [sizeSettingsPosition, setSizeSettingsPosition] = useState({ top: 100, left: 100 });
  const [showAddSettingsModal, setShowAddSettingsModal] = useState(false);
  
  // 任务列表状态
  const [generationTasks, setGenerationTasks] = useState([]);
  
  const { updateTask } = useTaskContext();
  
  // 页面刷新/关闭提示
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）';
        return e.returnValue;
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);

  // 初始化和清理Masonry布局
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      // 销毁已存在的实例
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }

      // 创建新实例
      masonryRef.current = new Masonry(showcaseRef.current, {
        itemSelector: '.showcase-item',
        columnWidth: '.showcase-item',
        percentPosition: true,
        transitionDuration: '0.3s',
        initLayout: true,
        gutter: 16,
        fitWidth: false,
        horizontalOrder: true
      });

      // 监听窗口大小变化
      const handleResize = () => {
        if (masonryRef.current) {
          setTimeout(() => {
            masonryRef.current.layout();
          }, 100);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (masonryRef.current) {
          masonryRef.current.destroy();
          masonryRef.current = null;
        }
      };
    }
  }, [activeTab]);

  // 监听图片加载
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      const images = showcaseRef.current.getElementsByTagName('img');
      let loadedCount = 0;

      const handleImageLoad = () => {
        loadedCount++;
        if (masonryRef.current) {
          masonryRef.current.layout();
        }
      };

      Array.from(images).forEach(img => {
        if (img.complete) {
          handleImageLoad();
        } else {
          img.addEventListener('load', handleImageLoad);
        }
      });

      return () => {
        Array.from(images).forEach(img => {
          img.removeEventListener('load', handleImageLoad);
        });
      };
    }
  }, [activeTab]);

  // 处理文件上传
  const handleFileUpload = (file) => {
    setShowUploadGuide(false);
  };

  // 处理原始图片上传结果
  const handleSourceUploadResult = (results) => {
    setHasUnsavedChanges(true);
    console.log('原始图片上传结果:', results);
    
    try {
      if (results.type === 'panels') {
        if (currentReuploadSourceImagePanelId) {
          // 如果是重新上传，替换原有面板
          setSourceImagePanels(prevPanels => 
            prevPanels.map(panel => 
              panel.componentId === currentReuploadSourceImagePanelId 
                ? { 
                    ...results.panels[0], 
                    componentId: currentReuploadSourceImagePanelId, 
                    componentType: 'sourceImagePanel', // 确保设置正确的组件类型
                    type: 'source',
                    source: results.panels[0].source || 'upload' // 保留source属性或设置默认值
                  }
                : panel
            )
          );
          // 重置当前重新上传的面板ID
          setCurrentReuploadSourceImagePanelId(null);
        } else {
          // 如果是新上传，添加新面板
          const panelsWithType = results.panels.map(panel => ({
            ...panel,
            componentType: 'sourceImagePanel', // 确保每个面板都有正确的组件类型
            type: 'source',
            source: panel.source || 'upload' // 保留source属性或设置默认值
          }));
          setSourceImagePanels(prevPanels => [...prevPanels, ...panelsWithType]);
        }
        
        // 移除对显示提示的设置
        // setShowTips(true);
      } else if (results.type === 'update') {
        // 处理面板更新（上传完成或失败的回调）
        console.log('收到面板更新:', results.panel);
        
        if (results.panel) {
          const updatedPanel = results.panel;
          
          // 检查是否包含服务器处理完成的标志
          const isUploadCompleted = updatedPanel.serverFileName || 
                                   (updatedPanel.processInfo && Object.keys(updatedPanel.processInfo).length > 0);
          
          // 强制设置完成状态的标志
          const forceCompleted = isUploadCompleted && (updatedPanel.status === 'uploading' || updatedPanel.status === 'processing');
          
          if (forceCompleted) {
            console.log('检测到上传已完成但状态未更新，强制设置为completed状态');
          }
          
          // 更新对应ID的面板
          setSourceImagePanels(prevPanels => 
            prevPanels.map(panel => {
              if (panel.componentId === updatedPanel.componentId) {
                console.log(`更新面板 ${panel.componentId}:`);
                console.log('- 原始状态:', panel.status);
                console.log('- 服务器返回状态:', updatedPanel.status);
                console.log('- 最终状态:', forceCompleted ? 'completed' : (updatedPanel.status || 'completed'));
                
                // 返回更新后的面板，确保保留所有重要字段
                return {
                  ...panel,
                  // 如果检测到上传完成标志但状态仍为uploading或processing，则强制设为completed
                  status: forceCompleted ? 'completed' : (updatedPanel.status || 'completed'),
                  errorMessage: updatedPanel.errorMessage,
                  processedFile: updatedPanel.processedFile,
                  processedUrl: updatedPanel.processedUrl,
                  processInfo: updatedPanel.processInfo,
                  serverFileName: updatedPanel.serverFileName || panel.serverFileName,
                  source: updatedPanel.source || panel.source // 保留source属性
                };
              }
              return panel;
            })
          );
        }
      } else if (results.type === 'error') {
        console.error('上传错误:', results.error);
        message.error('上传失败: ' + results.error);
        // 移除处理中的面板
        setSourceImagePanels(prevPanels => 
          prevPanels.filter(panel => panel.status !== 'processing'));
        // 重置当前重新上传的面板ID
        setCurrentReuploadSourceImagePanelId(null);
      }
    } catch (error) {
      console.error('上传结果处理失败:', error);
      message.error('上传结果处理失败: ' + (error.message || '未知错误'));
      // 移除处理中的面板
      setSourceImagePanels(prevPanels => 
        prevPanels.filter(panel => panel.status !== 'processing'));
      // 重置当前重新上传的面板ID
      setCurrentReuploadSourceImagePanelId(null);
    }
  };

  // 处理原始图片上传
  const handleSourceUploadChange = async (files) => {
    const  {fileInfos}=await uploadFiles(files,"upscale"); 
    if(fileInfos){
      const resultData = fileInfos[0];
      const imageId = generateId(ID_TYPES.COMPONENT);
      const updatedPanel = {
        ...sourceImagePanels[0],
        fileInfo: {
          ...sourceImagePanels[0],
          width: resultData.width,
          height: resultData.height,
          format: resultData.format,
          ...resultData
        }
      };
      
      console.log('更新后的面板数据:', JSON.stringify(updatedPanel, null, 2));
      
      // 更新面板中的图片信息
      setSourceImagePanels(prevPanels => {
        const newPanels = prevPanels.map(p => 
          p.componentId === imageId ? updatedPanel : p
        );
        console.log('更新后的sourceImagePanels数组:', JSON.stringify(newPanels, null, 2));
        return newPanels;
      });
    }
  };

  // 处理删除原始图片面板
  const handleDeleteSourceImagePanel = (panelId) => {
    setSourceImagePanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
  };

  // 处理重新上传原始图片
  const handleReuploadSource = (panel) => {
    if (panel && panel.componentId) {
      // 第一步：删除当前面板
      handleDeleteSourceImagePanel(panel.componentId);
      
      // 第二步：延迟一点点时间后触发上传区域点击（确保UI已更新）
      setTimeout(() => {
        // 直接触发上传区域的点击事件
        const uploadBox = document.getElementById('source-upload-box');
        if (uploadBox) {
          uploadBox.click();
        }
      }, 50);
    }
  };

  // 处理原始图片状态变化
  const handleSourceImageStatusChange = (panelId, newStatus) => {
    setSourceImagePanels(prevPanels =>
      prevPanels.map(panel =>
        panel.componentId === panelId ? { ...panel, status: newStatus } : panel
      )
    );
  };

  // 处理开始生成按钮点击
  const handleGenerate = async () => {
    setIsProcessing(true);
    console.log('开始生成...');
    let  taskData =null;
    try {
      // 检查是否有原始图片面板
      if (sourceImagePanels.length === 0) {
        message.error('请先上传原始图片');
        setIsProcessing(false);
        return;
      }
      
      // 检查是否已设置放大倍数和尺寸
      if (!savedSizeSettings) {
        message.error('请先设置放大倍数和尺寸');
        // 自动打开放大倍数设置弹窗
        handleMagnificationSizeClick({});
        setIsProcessing(false);
        return;
      }
      const balance = await checkUserBalance('高清放大', 'upscale', 1);
      if(balance.code !== 200){
        message.error(balance.message);
setIsProcessing(false);
        return;
      }
      // 获取当前用户ID
      const currentUserId = userId || getCurrentUserId();

      // 创建一个新的任务ID
      const taskId = generateId(ID_TYPES.TASK);
      
      // 处理自定义上传的图片
      let sourceToUse = sourceImagePanels[0];
      
      // 从TaskPanel拖拽过来的图片也需要上传到服务器
      if ((sourceToUse.file && sourceToUse.source === 'upload') ||
          (sourceToUse.source === 'upload' && !sourceToUse.file)) {
        
        // 显示上传中提示
        message.loading('正在上传图片...', 0);
        
        try {
          let fileToUpload = sourceToUse.file;
          
          // 如果没有file对象但有URL，需要从URL获取文件
          if (!fileToUpload && sourceToUse.url) {
            try {
              const response = await fetch(sourceToUse.url);
              const blob = await response.blob();
              fileToUpload = new File([blob], sourceToUse.serverFileName || 'image.jpg', {
                type: blob.type || 'image/jpeg'
              });
            } catch (error) {
              console.error('从URL获取文件失败:', error);
              message.error('图片处理失败，请重试');
              setIsProcessing(false);
              return;
            }
          }
          
          // 将文件上传到服务器
          const uploadResult = await uploadFiles([fileToUpload], "upscale");
          if (uploadResult) {
            const resultData = uploadResult.fileInfos[0];
            sourceToUse = {
              ...sourceToUse,
              image: resultData.url, // 使用构建的服务器URL
              url: resultData.url, // 同时设置url属性以保持一致性
              serverFileName: resultData.serverFileName,
              source: 'history',
              file: undefined, // 清除file属性，避免重复上传和存储不必要的数据
              fileInfo: {
                ...(sourceToUse.fileInfo || {}),
                ...resultData
              }
            };
            
            message.success('图片上传成功');
          } else {
            message.error('图片上传失败');
            setIsProcessing(false);
            return;
          }
        } catch (error) {
          console.error('上传图片时出错:', error);
          message.error('图片上传失败: ' + (error.message || '未知错误'));
          setIsProcessing(false);
          return;
        } finally {
          // 关闭上传中提示
          message.destroy();
        }
      }
      
      // 构建任务对象，使用服务器上的图片URL
       taskData = {
        taskId: taskId,
        userId: currentUserId,
        createdAt: new Date(),
        status: 'processing',
        imageCount: 1, // 固定为1张图片
        taskType: 'upscale', // 指定任务类型为高清放大
        pageType: 'upscale', // 添加页面类型
        requiresImage: true, // 明确需要图片
        // 修改components为数组形式，与ImageDetailsModal期望的数据结构一致
        components: [
          {
            componentType: 'sourceImagePanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '原始图片',
            title: '原始图片',
            status: 'completed',
            // originalImage: sourceToUse.url, // 使用更新后的URL
            url: sourceToUse.url, // 使用更新后的URL
            serverFileName: sourceToUse.serverFileName,
            fileInfo: {
              ...(sourceToUse.fileInfo || {}),
              serverFileName: sourceToUse.serverFileName // 确保在fileInfo中也设置serverFileName
            },
            isMainImage: true, // 标记为主图片
            source: sourceToUse.source || 'upload' // 来源为直接上传
          },
          {
            componentType: 'magnificationSize',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '放大倍数',
            status: 'completed',
            // 使用保存的尺寸和放大倍数设置
            ...savedSizeSettings
          }
        ],
        generatedImages: Array(1).fill(null).map((_, index) => ({ // 固定生成1张图片
          imageIndex: index,
          status: 'processing'
        })),
        processInfo:{
          results:[]
        }
      };
      
      console.log('生成任务数据:', taskData);
      
      setIsProcessing(true);
      
      // 先添加到本地状态，使UI立即响应
      if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      await createFlowTask(taskData);
      const {promptId,instanceId,status,url} = await executeFlow(WORKFLOW_NAME.UPSCALE,{
        "15":{
          "Number": savedSizeSettings.scale
        },
        "16":{
      "url": sourceToUse.url
        },
        "subInfo":{
          "type": "upscale",
          "title":"高清放大",
          "count":1
        }
      },taskData.taskId);
      setIsProcessing(false);
      setHasUnsavedChanges(false);
      if( generationAreaRef.current){
        taskData.promptId = promptId;
        taskData.instanceId = instanceId;
        taskData.url = url;
        taskData.newTask = true;
        generationAreaRef.current.setGenerationTasks(taskData);
    }
    } catch (error) {
      console.error('创建任务失败:', error);
      message.error('创建任务失败: ' + error.message);
      taskData.status = 'failed';
      setIsProcessing(false);
      setHasUnsavedChanges(false);
      setIsProcessing(false);
      if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      
      // 调用updateTask以触发失败提示音
      updateTask(taskData);
    }
  };


  // 处理编辑任务
  const handleEditTask = (task) => {
    if (!task) return;
    
    try {
      // 控制台记录任务数据
      console.log('回填任务数据:', task);
      
      console.log('任务组件:', task.components);
      
      // 处理组件数据 - 只接受标准数组格式
      const components = Array.isArray(task.components) ? task.components : [];
      console.log('处理的组件数据:', components);
      
      // 获取组件 - 使用小写组件名称匹配
      const sourceComponent = components.find(c => c.componentType === 'sourceImagePanel');
      const magnificationComponent = components.find(c => c.componentType === 'magnificationSize');
      
      if (sourceComponent) {
        console.log('获取到原始图片组件:', sourceComponent);
        setSourceImagePanels([
          {
            ...sourceComponent,
            title: sourceComponent.name || '原始图片',
            status: 'completed'
          }
        ]);
      } else {
        console.warn('未找到原始图片组件，请检查任务数据');
      }
      
      // 获取尺寸设置组件 - 使用小写组件名称匹配
      if (magnificationComponent) {
        console.log('获取到尺寸设置组件:', magnificationComponent);
        // 设置尺寸到savedSizeSettings用于后续生成
        setSavedSizeSettings(magnificationComponent);
        // 同样更新sizeSettings，确保UI显示正确
        setSizeSettings(magnificationComponent);
      } else {
        console.warn('未找到尺寸设置组件，使用默认设置');
        // 使用默认设置
        const defaultSize = {
          scale: 2,
          width: 1024,
          height: 1024
        };
        setSavedSizeSettings(defaultSize);
        setSizeSettings(defaultSize);
      }
      
      // 切换到结果标签页
      setActiveTab('result');
      
      message.success({
        content: '配置已重新导入，可继续进行调整',
        duration: 5
      });
      
      console.log('重新编辑任务:', {
        sourceImagePanel: sourceComponent,
        size: magnificationComponent,
        activeTab: 'result'
      });
    } catch (error) {
      console.error('恢复任务设置失败:', error);
      message.error('恢复任务设置失败: ' + (error.message || '未知错误'));
    }
  };

  // 添加点击外部关闭下拉菜单的处理
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdowns = document.querySelectorAll('.dropdown-menu');
      dropdowns.forEach(dropdown => {
        if (!dropdown.parentElement.contains(event.target)) {
          dropdown.classList.remove('show');
        }
      });
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const [currentTaskDetails, setCurrentTaskDetails] = useState(null);
  const [showTaskDetails, setShowTaskDetails] = useState(false);

  const handleViewDetails = useCallback((image, task) => {
    try {
      console.log('查看图片详情, 任务:', task);
      console.log('当前图片:', image);
      
      // 只接受标准数组格式
      const components = Array.isArray(task.components) ? task.components : [];
      
      // 查找源图片组件
      const sourceComponent = components.find(c => 
        c && (c.componentType === 'sourceImagePanel')
      );
      
      // 为ImageDetailsModal准备合适的数据结构
      const adaptedImage = {
        ...image,
        taskId: task.taskId,
        createdAt: task.createdAt,
        components: components, // 确保components是数组
        // 添加源图片信息，方便ImageDetailsModal识别
        hasSourceImagePanel: !!sourceComponent,
        sourceImagePanel: sourceComponent || null,
        // 添加关键文件名信息
        serverFileName: task.serverFileName || sourceComponent?.serverFileName || '未设置',
        primaryImageFileName: task.primaryImageFileName || sourceComponent?.serverFileName || '未设置',
      };
      
      // setShowImageDetails(true);
      return adaptedImage
      // 显示图片详情模态框
    } catch (error) {
      console.error('处理图片详情时出错:', error);
      message.error('加载图片详情失败');
    }
  }, []);

  // 添加关闭弹窗时的处理函数
  const handleCloseImageDetails = () => {
    // 直接关闭弹窗，不使用动画
    setShowImageDetails(false);
    
    // 重置状态，无需延迟
    setSelectedImage(null);
    // 重置任务信息，避免保留旧任务导致重新打开时出错
    setImageDetailsTask(null);
    setImagePosition({ x: 0, y: 0 });
    setIsDragging(false);
    lastPosition.current = { x: 0, y: 0 };
    setImageScale(100);
    setInitialScale(100);
    
    // 重置文本弹窗状态
    setShowAdvancedText(false);
  };

  // 添加点击外部关闭弹出层的处理函数
  useEffect(() => {
    const handleClickOutside = (event) => {
      // 检查点击是否发生在文本按钮上
      const isTextButton = event.target.closest('.text-button');
      // 检查点击是否发生在弹窗内部
      const isInsidePopup = event.target.closest('.text-popup');
      
      // 如果点击既不是文本按钮也不是弹窗内部，则关闭所有弹窗
      if (!isTextButton && !isInsidePopup) {
        setShowAdvancedText(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 计算初始缩放比例
  const calculateInitialScale = (img) => {
    if (!img) return 100;
    const container = img.parentElement;
    if (!container) return 100;
    
    // 计算图片在容器中的实际显示尺寸与真实尺寸的比例
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const imageNaturalWidth = img.naturalWidth;
    const imageNaturalHeight = img.naturalHeight;
    
    // 计算图片适应容器时的尺寸
    const containerRatio = containerWidth / containerHeight;
    const imageRatio = imageNaturalWidth / imageNaturalHeight;
    
    let scale;
    if (imageRatio > containerRatio) {
      // 图片较宽，以容器宽度为基准
      scale = (containerWidth / imageNaturalWidth) * 100;
    } else {
      // 图片较高，以容器高度为基准
      scale = (containerHeight / imageNaturalHeight) * 100;
    }

    // 如果计算出的缩放比例大于100%，说明图片实际尺寸小于容器
    // 这种情况下我们应该将图片显示为其实际大小
    if (scale > 100) {
      scale = 100;
    }
    
    // 返回相对于真实尺寸的缩放百分比，四舍五入到整数
    return Math.round(scale);
  };

  // 处理图片加载完成
  const handleImageLoad = (e) => {
    const img = e.target;
    const initialScaleValue = calculateInitialScale(img);
    setInitialScale(initialScaleValue);  // 设置初始比例
    setImageScale(initialScaleValue);    // 设置当前比例
    imageRef.current = img;
  };

  


  const handleMagnificationSizeClick = (event) => {
    // 调试信息，打印当前sourceImagePanels的详细信息
    console.log('点击放大倍数时的sourceImagePanels:', JSON.stringify(sourceImagePanels, null, 2));
    console.log('sourceImagePanels[0]?.fileInfo:', sourceImagePanels.length > 0 ? sourceImagePanels[0].fileInfo : null);
    
    // 设置位置并先打开弹窗，无论是否有图片尺寸
    const magnificationSizeComponents = document.querySelectorAll('.panel-component');
    let position;
    let found = false;
    
    // 遍历所有面板组件，找到放大倍数组件
    for (let i = 0; i < magnificationSizeComponents.length; i++) {
      const component = magnificationSizeComponents[i];
      const titleEl = component.querySelector('h3');
      if (titleEl && titleEl.textContent.includes('放大倍数')) {
        const rect = component.getBoundingClientRect();
        position = {
          top: Math.max(20, rect.top - 180), // 向上偏移180px，同时确保不会超出屏幕顶部
          left: rect.right + 20
        };
        
        // 确保弹窗不超出屏幕右侧边界
        if (position.left > window.innerWidth - 480) {
          position.left = window.innerWidth - 490;
        }
        
        found = true;
        break;
      }
    }
    
    // 如果找不到组件，使用默认位置
    if (!found) {
      const controlPanelEl = document.querySelector('.control-panel');
      if (controlPanelEl) {
        const rect = controlPanelEl.getBoundingClientRect();
        position = {
          top: Math.max(20, 100 - 180), // 向上偏移180px
          left: rect.right + 20
        };
      } else {
        // 最后的后备方案
        position = {
          top: Math.max(20, 100 - 180), // 向上偏移180px
          left: 350
        };
      }
    }
    
    // 设置位置并打开弹窗
    setSizeSettingsPosition(position);
    setShowSizeSettingsModal(true);
    
    // 如果没有图片，后续处理就不需要了
    if (sourceImagePanels.length === 0) {
      return;
    }
    
    // 检查是否有可用的原图和尺寸信息
    const hasImageInfoAvailable = sourceImagePanels.length > 0 && 
                               sourceImagePanels[0].fileInfo && 
                               sourceImagePanels[0].fileInfo.width && 
                               sourceImagePanels[0].fileInfo.height;
    
    if (!hasImageInfoAvailable) {
      // 有图片但宽高信息不可用，可能图片还在加载中
      message.info('正在获取图片信息，请稍候...');
      
      // 如果有文件对象，直接从文件对象获取尺寸，避免使用可能失效的blob URL
      if (sourceImagePanels[0].file && sourceImagePanels[0].file instanceof File) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const dataUrl = e.target.result;
          const tempImg = new Image();
          
          tempImg.onload = () => {
            // 获取图片宽高
            const updatedPanel = {
              ...sourceImagePanels[0],
              fileInfo: {
                ...sourceImagePanels[0].fileInfo || {},
                width: tempImg.naturalWidth,
                height: tempImg.naturalHeight,
                // originalUrl: dataUrl // 使用Data URL作为可靠的图片源
              }
            };
            
            // 更新面板信息
            setSourceImagePanels(prevPanels => 
              prevPanels.map(panel => 
                panel.componentId === sourceImagePanels[0].componentId ? updatedPanel : panel
              )
            );
            
            // 宽高信息更新后，重新渲染弹窗
            console.log('从文件读取图片尺寸信息已更新:', {
              width: tempImg.naturalWidth,
              height: tempImg.naturalHeight
            });
            
            // 轻微调整位置以触发组件重新渲染
            setSizeSettingsPosition(prev => ({
              top: prev.top,
              left: prev.left + 0.1 // 微小变化触发重新渲染
            }));
          };
          
          tempImg.onerror = () => {
            console.error('从Data URL加载图片失败');
            message.error('无法获取图片尺寸信息');
          };
          
          tempImg.src = dataUrl;
        };
        
        reader.onerror = () => {
          console.error('读取文件失败');
          message.error('读取文件失败');
        };
        
        reader.readAsDataURL(sourceImagePanels[0].file);
      }
      // 如果已有Data URL，直接使用
      else if (sourceImagePanels[0].fileInfo && sourceImagePanels[0].fileInfo.originalUrl && 
               sourceImagePanels[0].fileInfo.originalUrl.startsWith('data:')) {
        const img = new Image();
        img.onload = () => {
          // 获取图片宽高后更新面板信息
          const updatedPanel = {
            ...sourceImagePanels[0],
            fileInfo: {
              ...sourceImagePanels[0].fileInfo,
              width: img.naturalWidth,
              height: img.naturalHeight
            }
          };
          
          // 更新面板信息
          setSourceImagePanels(prevPanels => 
            prevPanels.map(panel => 
              panel.componentId === sourceImagePanels[0].componentId ? updatedPanel : panel
            )
          );
          
          // 宽高信息更新后，重新渲染弹窗
          console.log('图片尺寸信息已更新:', {
            width: img.naturalWidth,
            height: img.naturalHeight
          });
          
          // 轻微调整位置以触发组件重新渲染
          setSizeSettingsPosition(prev => ({
            top: prev.top,
            left: prev.left + 0.1 // 微小变化触发重新渲染
          }));
        };
        
        img.onerror = () => {
          console.error('无法加载图片获取尺寸');
          message.error('无法获取图片尺寸信息');
        };
        
        img.src = sourceImagePanels[0].fileInfo.originalUrl;
      }
      // 如果既没有文件也没有Data URL，则使用HTTP URL（如果有的话）
      else if (sourceImagePanels[0].url && 
               (sourceImagePanels[0].url.startsWith('http:') || 
                sourceImagePanels[0].url.startsWith('https:') || 
                sourceImagePanels[0].url.startsWith('/'))) {
        // 使用HTTP URL
        const img = new Image();
        img.crossOrigin = "Anonymous"; // 添加跨域支持
        
        img.onload = () => {
          // 获取图片宽高后更新面板信息
          const updatedPanel = {
            ...sourceImagePanels[0],
            fileInfo: {
              ...sourceImagePanels[0].fileInfo || {},
              width: img.naturalWidth,
              height: img.naturalHeight
            }
          };
          
          // 更新面板信息
          setSourceImagePanels(prevPanels => 
            prevPanels.map(panel => 
              panel.componentId === sourceImagePanels[0].componentId ? updatedPanel : panel
            )
          );
          
          // 宽高信息更新后，重新渲染弹窗
          console.log('图片尺寸信息已更新:', {
            width: img.naturalWidth,
            height: img.naturalHeight
          });
          
          // 轻微调整位置以触发组件重新渲染
          setSizeSettingsPosition(prev => ({
            top: prev.top,
            left: prev.left + 0.1 // 微小变化触发重新渲染
          }));
        };
        
        img.onerror = () => {
          console.error('无法加载HTTP URL图片获取尺寸');
          message.error('无法获取图片尺寸信息');
        };
        
        // 添加时间戳防止缓存
        const timestamp = Date.now();
        const separator = sourceImagePanels[0].url.includes('?') ? '&' : '?';
        const httpsUrl = sourceImagePanels[0].url.replace(/^http:/, 'https:');
        img.src = `${httpsUrl}${separator}t=${timestamp}`;
      }
      else {
        message.error('无法找到有效的图片来源');
      }
    }
  };
 

  // 监听sourceImagePanels变化，更新弹窗数据
  useEffect(() => {
    // 如果弹窗正在显示，并且sourceImagePanels已更新且有宽高信息，则重新设置位置以触发重新渲染
    if (showSizeSettingsModal && sourceImagePanels.length > 0 && sourceImagePanels[0].fileInfo) {
      console.log('sourceImagePanels更新，弹窗显示中，更新弹窗配置:', {
        width: sourceImagePanels[0].fileInfo.width,
        height: sourceImagePanels[0].fileInfo.height,
        "sourceImagePanels尺寸": sourceImagePanels[0].fileInfo
      });
      
      // 轻微调整位置以触发组件重新渲染
      setSizeSettingsPosition(prev => ({
        top: prev.top,
        left: prev.left + 0.1 // 微小变化触发重新渲染
      }));
    }
  }, [sourceImagePanels, showSizeSettingsModal]);

  // 放大倍数选择对话框
  const [showSizeModal, setShowSizeModal] = useState(false);


  // 组件卸载时清理临时URL
  useEffect(() => {
    // 组件卸载时清理资源
    return () => {
      // 清理sourceImagePanels中的临时URL
      sourceImagePanels.forEach(panel => {
        if (panel.url && panel.source === 'upload') {
          // 只释放本地上传的临时URL
          try {
            URL.revokeObjectURL(panel.url);
            console.log('已释放临时URL:', panel.url);
          } catch (error) {
            console.error('释放临时URL时出错:', error);
          }
        }
      });
    };
  }, []); // 移除sourceImagePanels依赖，只在组件卸载时执行

  return (
    <RequireLogin isLoggedIn={isLoggedIn} featureName="高清放大功能">
      <PromptIfUnsaved when={hasUnsavedChanges} message="操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）" />
      <div className="upscale-page">
        <div className="upscale-container" ref={containerRef}>
          <ControlPanel
            ref={controlPanelRef}
            width={`${controlPanelWidth}%`}
            onGenerate={handleGenerate}
            disabled={isProcessing}
            featureName="upscale"
            quantity={imageQuantity || 1}
          >
                    {/* 原始图片上传区域或原始图片面板 - 位于最上方 */}
                    {sourceImagePanels.length === 0 ? (
                      <UploadBox
                        id="source-upload-box"
                        onShowGuide={(files, isGalleryMode) => {
                        
                        }}
                        onUploadResult={handleSourceUploadResult}
                        onChange={handleSourceUploadChange}
                        panels={sourceImagePanels}
                        className="mt-2"
                        showSupportTag={false}
                        pageType="upscale"
                        uploadType="source"
                      />
                    ) : (
                      // 展示原始图片面板
                      sourceImagePanels.map((panel) => (
                        <SourceImagePanel
                          key={panel.componentId}
                          panel={panel}
                          onExpandClick={(panel, position) => {
                            // 创建增强的面板对象，避免警告
                            const enhancedPanel = {
                              ...panel,
                              // 标记为增强的原始图片面板以避免不必要的警告
                              isEnhancedSourceImagePanel: true, // 添加原始图片标记
                              processedFile: panel.processedFile || panel.preview || panel.url || panel.image,
                              fileInfo: panel.fileInfo || {
                                size: 500000, // 默认500KB
                                format: 'image/jpeg',
                                type: 'image/jpeg',
                                width: 800,
                                height: 1200
                              }
                            };
                            
                            setOperationsPanel({
                              panel: enhancedPanel,
                              position
                            });
                          }}
                          onDelete={() => handleDeleteSourceImagePanel(panel.componentId)}
                          onReupload={() => handleReuploadSource(panel)}
                          onStatusChange={(newStatus) => handleSourceImageStatusChange(panel.componentId, newStatus)}
                          isActive={panel.status === 'completed'}
                          onPanelsChange={setSourceImagePanels}
                          pageType="upscale"
                        />
                      ))
                    )}

                    {/* 放大倍数与尺寸设置 */}
                    <MagnificationSize
                      onExpandClick={handleMagnificationSizeClick}
                      savedSettings={sizeSettings}
                    />
          </ControlPanel>

          <ResizeHandle
            ref={handleRef}
            containerRef={containerRef}
            onResize={setControlPanelWidth}
            minWidth={25}
            maxWidth={50}
          />

                  <GenerationArea
            ref={generationAreaRef}    setIsProcessing={setIsProcessing}

            activeTab={activeTab}
            onTabChange={setActiveTab}
            tasks={Array.isArray(generationTasks) ? generationTasks : []}
            onEditTask={handleEditTask}
            onViewDetails={handleViewDetails}
            pageType="upscale"
          />
        </div>

        {/* 上传指南模态框 */}
        {showUploadGuide && (
          <UploadGuideModal
            type={uploadGuideType}
            pageType="upscale"
            onClose={() => setShowUploadGuide(false)}
            onUpload={(result) => {
              console.log('收到上传结果:', result);
              // 根据结果中的shouldClose字段决定是否关闭弹窗
              if (result.shouldClose !== false) {
                setShowUploadGuide(false);
              }
            }}
          />
        )}

        {/* 添加操作弹窗 */}
        {operationsPanel && (
          <ImageInfoModal
            panel={operationsPanel.panel}
            position={operationsPanel.position}
            onClose={() => setOperationsPanel(null)}
            onDelete={() => handleDeleteSourceImagePanel(operationsPanel.panel.componentId)}
            onReupload={() => handleReuploadSource(operationsPanel.panel)}
            pageType="upscale"
          />
        )}

        {/* 添加 ImageDetailsModal 组件 - 使用懒加载 */}
        {showImageDetails && selectedImage ? (
          <MemoizedImageDetailsModal
            visible={showImageDetails}
            onClose={handleCloseImageDetails}
            selectedImage={selectedImage}
            generationTasks={generationTasks}
            onEditTask={handleEditTask}
            pageType="upscale"
          />
        ) : null}



        {/* 显示生成中的加载状态 */}
        {isProcessing && (
          <div className="generating-status">
            <Spin size="large" />
            <p>正在生成中，请稍候...</p>
          </div>
        )}

        {/* 添加操作弹窗 */}
        {showAddSettingsModal && (
          <Modal
            title="添加操作"
            open={showAddSettingsModal}
            onCancel={() => setShowAddSettingsModal(false)}
            footer={null}
            destroyOnClose
          >
            <div className="settings-modal-content">
              {/* 添加操作内容 */}
            </div>
          </Modal>
        )}
        
        {/* 放大倍数与尺寸设置弹窗 */}
        <MagnificationSizeModal
          visible={showSizeSettingsModal}
          onClose={() => setShowSizeSettingsModal(false)}
          onApply={(settings) => {
            console.log('接收到尺寸设置:', JSON.stringify(settings));
            // 确保保存完整设置信息，包括activeTab和activePlatform
            setSizeSettings(settings);
            // 当用户点击确定时，保存设置到savedSizeSettings
            setSavedSizeSettings(settings);
            setHasUnsavedChanges(true);
          }}
          defaultScale={sizeSettings?.scale || 2}
          originalWidth={sourceImagePanels.length > 0 && sourceImagePanels[0].fileInfo ? sourceImagePanels[0].fileInfo.width : null}
          originalHeight={sourceImagePanels.length > 0 && sourceImagePanels[0].fileInfo ? sourceImagePanels[0].fileInfo.height : null}
          originalImage={sourceImagePanels.length > 0 ? (
            // 优先使用持久化的图片URL
            (sourceImagePanels[0].fileInfo && sourceImagePanels[0].fileInfo.originalUrl) || 
            // 如果没有持久化URL，尝试使用其他可能的图片URL
            sourceImagePanels[0].url || 
            sourceImagePanels[0].originalImage || 
            sourceImagePanels[0].preview || 
            null
          ) : null}
          style={{
            top: sizeSettingsPosition.top,
            left: sizeSettingsPosition.left,
          }}
          savedSettings={savedSizeSettings}
        />
      </div>
    </RequireLogin>
  );
};

export default UpscalePage; 