const url = require('node:url');

const {client} = require('ali-oss/lib/setConfig');

const MongoDBCache = require('../../../utils/cacheUtils');
const CreditService = require('../../credits/creditService');
const ComfyClientFactory = require('../ComfyClientFactory');

class WorkflowService {
    constructor(workflowManager, messageProcessor) {
        this.workflowManager = workflowManager;
        this.messageProcessor = messageProcessor;
        this.ossClient = require('../../../utils/ossUtils');
        this.cache = new MongoDBCache();
    }

    async executeWorkflow(workflowName, inputData, userId, taskId = null, callback) {
        let instanceIdTmp= null;
        try {
            // 1. 扣除用户算力
            const subType = inputData.subInfo.type;
            const subTitle = inputData.subInfo.title;

            await CreditService.consumeCredits(
                userId,
                subTitle,
                subType,
                `执行${subTitle}工作流`,
                taskId,
                null,
                inputData.subInfo.count
            );
            console.log('获取ComfyClient实例开始============');
            // 2. 获取ComfyClient实例，传递用户ID以优先选择历史实例
            const comfyClient = await ComfyClientFactory.createClient(inputData.token);
            console.log('获取ComfyClient实例成功============');
            instanceIdTmp=comfyClient.instanceId;
            console.log('获取并填充工作流模板开始================');
            // 3. 获取并填充工作流模板
            const populatedWorkflow = this.messageProcessor.populateWorkflow(workflowName, inputData);
            console.log('获取并填充工作流模板成功================');
            console.log('执行工作流并立即返回开始===================');
            // 4. 执行工作流并立即返回
            const {promptId,flowResult} = await comfyClient.executeWorkflow(populatedWorkflow, null, this.callback);
            console.log('执行工作流并立即返回成功===================');

            // 回调通知进度
            callback(promptId, comfyClient.getInstanceId(),comfyClient.getUrl());

            // 直接返回 promptId 和实例 id
            return {
                success: true,
                promptId: promptId,
                instanceId: comfyClient.getInstanceId(),
                url: comfyClient.getUrl(),
                client: comfyClient,
                flowResult,
                populatedWorkflow
            };

        } catch (error) {
            // 如果是算力相关错误，直接返回错误信息
            if (error.status === 402 || error.status === 404) {
                return {
                    success: false,
                    error: error.message,
                    code: error.status
                };
            }
            try {
                await ComfyClientFactory.finishUse(instanceIdTmp);
            }catch (error) {

            }
            return {
                success: false,
                error: error.message
            };
        }
    }
    callback( id,value,max) {
        console.log('回调执行');
        console.log(id,value,max);
    }

    async getWorkflowResult(promptId, workflowName) {
        try {
            const status = await this.comfyUIClient.getWorkflowStatus(promptId);
            const outputs = await this.comfyUIClient.getWorkflowOutputs(promptId);

            const processedResponse = this.messageProcessor.processResponse(
                { ...status, output: outputs },
                workflowName
            );

            return {
                success: true,
                data: processedResponse
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async processTxt(comfyClient,populatedWorkflow) {
        const inputs = populatedWorkflow['32'].inputs;
        const filename = inputs.filename_prefix + inputs.filename_suffix + inputs.file_extension;
        const txt = await comfyClient.getTxt('output/' + filename);
        return txt;
    }

    /**
     * 从工作流名称获取功能类型
     * @private
     */
    _getWorkflowType(workflowName) {
        // 工作流名称格式: B01-fashion, C01-extract 等
        // 提取功能名称部分
        const match = workflowName.match(/[A-Z]\d+-(.+)/);
        if (match && match[1]) {
            return match[1];
        }
        throw new Error(`无效的工作流名称格式: ${workflowName}`);
    }

    async processWorkflowResult(promptId, instanceId, workflowName,comfyClient,executeResult) {
        try {
            console.log('异步获取工作流结果',promptId,instanceId,workflowName);
            const executionResponse = await comfyClient.getResult(promptId);
            console.log('异步获取工作流结果成功',promptId,instanceId,workflowName);
            // 处理响应
            const processedResponse = this.messageProcessor.processResponse(
                executionResponse,
                workflowName
            );

            // 处理图片上传
            if (processedResponse.outputs && processedResponse.outputs.images) {
                for (const image of processedResponse.outputs.images) {
                    if (image.filename) {
                        try {
                            const ossResult = await this.ossClient.transferImage(`output/${image.filename}`, comfyClient);
                            image.ossUrl = ossResult.url;
                            for (const key in ossResult) {
                                image[key] = ossResult[key];
                            }
                        } catch (error) {
                            console.error(`上传图片${image.filename}到OSS失败:`, error);
                        }
                    }
                }
            }

            // 特殊工作流处理
            if (workflowName === 'C01-extract') {
                processedResponse.outputs.prompt = await this.processTxt(comfyClient, executeResult.populatedWorkflow);
            }

            await ComfyClientFactory.finishUse(instanceId);
            return {
                success: true,
                data: processedResponse,
                promptId: executionResponse.prompt_id
            };
        } catch (error) {
            console.error(error);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

module.exports = WorkflowService;