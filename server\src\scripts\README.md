# 工作流插入脚本使用说明

本目录包含了用于管理工作流数据的脚本工具，可以根据ComfyUI的JSON工作流文件自动生成并插入工作流记录到数据库中。

## 快速开始

### 使用主管理脚本（推荐）

最简单的方式是使用主管理脚本：

```bash
# 交互式菜单（推荐新手使用）
node server/src/scripts/workflowManager.js

# 快速插入工作流
node server/src/scripts/workflowManager.js quick

# 验证数据完整性
node server/src/scripts/workflowManager.js validate

# 查看帮助
node server/src/scripts/workflowManager.js help
```

## 脚本列表

### 0. `workflowManager.js` - 主管理脚本（推荐）

统一的工作流管理入口，提供交互式菜单和命令行接口。

**使用方法:**
```bash
# 交互式菜单
node server/src/scripts/workflowManager.js

# 命令行模式
node server/src/scripts/workflowManager.js <command> [options]
```

**可用命令:**
- `quick` - 快速插入工作流
- `insert` - 完整插入工作流
- `update` - 更新工作流信息
- `validate` - 验证数据完整性
- `status` - 查看系统状态
- `help` - 显示帮助信息

### 1. `insertWorkflowsFromJson.js` - 完整工作流插入脚本

功能最全面的工作流插入脚本，支持详细配置和多种选项。

**使用方法:**
```bash
# 基本使用
node server/src/scripts/insertWorkflowsFromJson.js

# 清空现有数据重新插入
node server/src/scripts/insertWorkflowsFromJson.js --clear

# 仅预览将要插入的数据，不实际插入
node server/src/scripts/insertWorkflowsFromJson.js --dry-run

# 指定创建者用户ID
node server/src/scripts/insertWorkflowsFromJson.js --user-id 507f1f77bcf86cd799439011
```

**功能特性:**
- ✅ 自动扫描 `apiJson` 目录中的所有JSON文件
- ✅ 根据文件名自动分类（A系列=款式设计，B系列=模特图，C系列=工具）
- ✅ 详细的工作流描述和标签配置
- ✅ 智能优先级分配
- ✅ 支持清空现有数据
- ✅ 支持预览模式
- ✅ 详细的统计信息显示

### 2. `quickInsertWorkflows.js` - 快速插入脚本

简化版的快速插入脚本，适合日常使用。

**使用方法:**
```bash
node server/src/scripts/quickInsertWorkflows.js
```

**功能特性:**
- ✅ 快速插入，无需复杂配置
- ✅ 自动跳过已存在的工作流
- ✅ 基于预定义配置快速生成
- ✅ 显示插入结果统计

### 3. `validateWorkflows.js` - 数据验证脚本

验证工作流数据的完整性和一致性。

**使用方法:**
```bash
node server/src/scripts/validateWorkflows.js
```

**功能特性:**
- ✅ 验证JSON文件与数据库记录的一致性
- ✅ 检查数据格式和必填字段
- ✅ 分析分类和优先级分布
- ✅ 生成健康度评分和建议
- ✅ 发现缺失或多余的工作流

### 4. `updateWorkflowsInfo.js` - 批量更新脚本

用于批量更新现有工作流的信息。

**使用方法:**
```bash
# 更新所有字段
node server/src/scripts/updateWorkflowsInfo.js

# 仅预览更新内容
node server/src/scripts/updateWorkflowsInfo.js --dry-run

# 仅更新指定字段
node server/src/scripts/updateWorkflowsInfo.js --field=name
node server/src/scripts/updateWorkflowsInfo.js --field=description
node server/src/scripts/updateWorkflowsInfo.js --field=tags
node server/src/scripts/updateWorkflowsInfo.js --field=priority
```

**功能特性:**
- ✅ 批量更新工作流信息
- ✅ 支持选择性字段更新
- ✅ 预览模式避免误操作
- ✅ 详细的统计信息

## 工作流配置说明

### 分类规则

脚本根据工作流ID的前缀自动分类：

- **A系列** → `款式设计` - 专注于服装设计和款式开发
- **B系列** → `模特图` - 处理模特图片和展示效果
- **C系列** → `工具` - 提供辅助功能

### 优先级设置

优先级范围：0-100，数字越大优先级越高

- **90-100**: 核心功能（如爆款开发、自动换装）
- **70-89**: 重要功能（如时尚大片、款式优化）
- **50-69**: 常用功能（如重新配色、背景处理）
- **30-49**: 专业功能（如面料生成、灵感探索）
- **10-29**: 特殊功能（如绘图设计、图像扩展）
- **1-9**: 实验功能（如细节迁移、手部修复）

### 支持的工作流

当前支持的工作流ID和对应名称：

| 工作流ID | 名称 | 分类 | 优先级 |
|---------|------|------|--------|
| A01-trending | 爆款开发 | 款式设计 | 90 |
| A02-optimize | 款式优化 | 款式设计 | 70 |
| A03-inspiration | 灵感探索 | 款式设计 | 45 |
| B01-fashion | 时尚大片 | 模特图 | 75 |
| B02-tryonauto | 自动换装 | 模特图 | 85 |
| B04-recolor | 重新配色 | 模特图 | 60 |
| C02-upscale | 图像放大 | 工具 | 55 |
| C03-mattingbg | 背景抠图 | 工具 | 80 |
| ... | ... | ... | ... |

## 使用流程

### 首次设置

1. **确保数据库连接**
   ```bash
   # 检查 .env 文件中的数据库配置
   MONGODB_URI=mongodb://localhost:27017/aibikini
   ```

2. **确保有管理员用户**
   ```bash
   # 如果没有管理员用户，需要先创建
   # 或使用 --user-id 参数指定现有用户
   ```

3. **运行快速插入脚本**
   ```bash
   # 使用主管理脚本（推荐）
   node server/src/scripts/workflowManager.js quick

   # 或直接运行
   node server/src/scripts/quickInsertWorkflows.js
   ```

4. **验证数据完整性**
   ```bash
   node server/src/scripts/workflowManager.js validate
   ```

### 日常维护

#### 使用主管理脚本（推荐）

```bash
# 交互式菜单，适合新手
node server/src/scripts/workflowManager.js

# 快速插入新工作流
node server/src/scripts/workflowManager.js quick

# 验证数据完整性
node server/src/scripts/workflowManager.js validate

# 更新工作流信息（预览模式）
node server/src/scripts/workflowManager.js update --dry-run
```

#### 传统方式

1. **添加新工作流**
   - 将新的JSON文件放入 `apiJson` 目录
   - 在脚本中添加对应的配置
   - 运行快速插入脚本

2. **更新工作流信息**
   ```bash
   # 预览更新内容
   node server/src/scripts/updateWorkflowsInfo.js --dry-run

   # 执行更新
   node server/src/scripts/updateWorkflowsInfo.js
   ```

3. **重新初始化**
   ```bash
   # 清空并重新插入所有工作流
   node server/src/scripts/insertWorkflowsFromJson.js --clear
   ```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MongoDB服务是否启动
   - 确认 `.env` 文件中的数据库URI正确

2. **未找到管理员用户**
   - 确保数据库中有 `role: 'admin'` 的用户
   - 或使用 `--user-id` 参数指定用户ID

3. **工作流已存在**
   - 脚本会自动跳过已存在的工作流
   - 使用 `--clear` 参数可以清空重新插入

4. **JSON文件解析失败**
   - 检查JSON文件格式是否正确
   - 确认文件编码为UTF-8

### 调试技巧

1. **使用预览模式**
   ```bash
   node server/src/scripts/insertWorkflowsFromJson.js --dry-run
   ```

2. **查看详细日志**
   - 脚本会输出详细的执行日志
   - 注意查看错误信息和警告

3. **检查数据库状态**
   ```bash
   # 连接MongoDB查看数据
   mongo aibikini
   db.workflows.find().count()
   db.workflows.find({}, {id:1, name:1, category:1})
   ```

## 扩展开发

### 添加新工作流

1. **在配置中添加新工作流**
   ```javascript
   // 在 WORKFLOW_CONFIG 中添加
   'D01-newworkflow': { 
     name: '新工作流', 
     category: '其他', 
     priority: 50 
   }
   ```

2. **添加详细信息**
   ```javascript
   // 在 WORKFLOW_UPDATES 中添加详细配置
   'D01-newworkflow': {
     name: '新工作流',
     description: '详细描述...',
     tags: ['标签1', '标签2'],
     priority: 50
   }
   ```

### 自定义分类

修改 `CATEGORY_MAPPING` 来添加新的分类规则：

```javascript
const CATEGORY_MAPPING = {
  'A': '款式设计',
  'B': '模特图',
  'C': '工具',
  'D': '新分类'  // 添加新分类
};
```

### 批量操作

可以基于现有脚本开发更多批量操作功能：

- 批量启用/禁用工作流
- 批量修改优先级
- 导出/导入工作流配置
- 工作流使用统计分析

## 安全注意事项

1. **备份数据**
   ```bash
   # 在执行清空操作前备份数据
   mongoexport --db aibikini --collection workflows --out workflows_backup.json
   ```

2. **使用预览模式**
   - 重要操作前先使用 `--dry-run` 预览

3. **权限控制**
   - 确保只有管理员可以执行这些脚本
   - 生产环境中谨慎使用 `--clear` 参数

4. **版本控制**
   - 将脚本配置的修改纳入版本控制
   - 记录重要的数据变更操作

通过这些脚本，您可以高效地管理工作流数据，确保系统中的工作流信息始终保持最新和准确。
