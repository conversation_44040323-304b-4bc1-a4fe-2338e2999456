const axios = require('axios');
const WebSocket = require('ws');
const Config = require('config');
const ossClient = require('../../utils/ossUtils');
const instanceService = require('../instance/instanceService');
const MongoDBCache = require("../../utils/cacheUtils");

// 创建缓存实例
const cache = new MongoDBCache({
  collectionName: 'instance_status_cache',
  defaultTTL: 24 * 60 * 60 * 1000 // 60秒缓存
});


const comfyUiConfig = Config.get('comfyUi');
const promptPrefix = '/prompt'


class ComfyClient {
  constructor(url,
  instanceId
) {
    // 设置基础URL
    this.baseURL = url;
    console.log('ComfyUI基础URL:', this.baseURL);
    // 从环境变量读取API密钥和实例ID
    this.apiKey = comfyUiConfig.apiKey || '';
    this.instanceId = instanceId || '';

    // 创建axios实例
    this.axiosInstance = axios.create({
      baseURL: "http://"+this.baseURL,
      timeout: 60000,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Instance-ID': this.instanceId
      }
    });

    // 初始化WebSocket连接
    this.ws = null;
    this.wsUrl = `ws://${this.baseURL}/ws`;
    this.callbacks = new Map();
    // this.initWebSocket();
  }
  /**
   * 检查ComfyUI服务器是否运行成功
   * @returns {Promise<boolean>} 返回true表示运行成功，false表示失败
   */
  async checkServerStatus() {
    try {
      // 1. 检查基础API端点（如/prompt或系统信息接口）
      const response = await this.axiosInstance.get('/system_stats', {
        timeout: 5000,
      });

      // 2. 验证响应状态和数据
      if (response!=  null && response!==  {}  && response.status === 200 && response.data) {
        console.log(this.instanceId+'ComfyUI服务器运行正常');
        return true;
      }
      return false;
    } catch (error) {
      console.error(this.instanceId+'ComfyUI服务器健康检查失败，可能还没启动');
      return false;
    }
  }

  /**
   * 初始化并验证服务器状态（带重试机制）
   * @param {number} maxRetries 最大重试次数（默认3次）
   * @param {number} retryDelay 重试间隔（毫秒，默认2000ms）
   */
  async initializeWithRetry(maxRetries = 20, retryDelay = 10000) {
    let retries = 0;
    while (retries < maxRetries) {
      try {
        const isReady = await this.checkServerStatus();
        if (isReady) {
          console.log('ComfyUI服务器初始化成功！');
          this.initWebSocket(); // 初始化WebSocket连接
          return;
        }
      } catch (error) {
        console.error(`初始化尝试 ${retries + 1}/${maxRetries} 失败:`, error.message);
      }

      retries++;
      if (retries < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
    throw new Error(`ComfyUI服务器初始化失败，超过最大重试次数（${maxRetries}）`);
  }
  // 初始化WebSocket连接
  initWebSocket() {
   
  }

  // 执行工作流
  async executeWorkflow(workflow, params = {}, progressCallback) {
    try {
      const response = await this.axiosInstance.post(promptPrefix, {
        prompt: workflow
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 30000
      });

      if (response.status !== 200) {
        throw new Error(`执行失败: ${response.status}`);
      }
      const promptId = response.data['prompt_id'];
      
      // 如果提供了回调函数，则注册到callbacks中
      if (progressCallback) {
        this.callbacks.set(promptId, progressCallback);
      }
      console.log(promptId)

      return {promptId,flowResult:response.data};
    } catch (error) {
      console.error('执行工作流时出错:', error);
      throw new Error(`执行工作流失败: ${error.message}`);
    }
  }

//   /view?filename=anime 获取文件
  async getFile(filename) {
    const apiPath = `/view?filename=${filename}`;
    const response = await this.axiosInstance.get(apiPath, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Instance-ID': this.instanceId
      },
      responseType: 'stream'
    });
    return response;
  }
  async streamToString(stream) {
    const chunks = [];
    for await (const chunk of stream) {
      chunks.push(chunk);
    }
    return Buffer.concat(chunks).toString('utf-8');
  }
//   /view?filename=anime 获取文件
  async getTxt(filename) {
    const apiPath = `/view?filename=${filename}`;
    const response = await this.axiosInstance.get(apiPath, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Instance-ID': this.instanceId
      },
      responseType: 'stream'
    });
    return this.streamToString(response.data);
  }

// 获取结果方法
  async getResult(promptId) {
    try {
      // 使用确认的API路径前缀
      const apiPath = `/history/${promptId}`;

      const response = await this.axiosInstance.get(apiPath, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'X-Instance-ID': this.instanceId
        }
      });
      // console.log('云实例响应结果:', response.data);
      
      // 如果返回数据为空,等待一会后重试
      if (!response.data || Object.keys(response.data).length === 0) {
        instanceService.recordInstanceActivity(this.instanceId)
        await new Promise(resolve => setTimeout(resolve, 4000)); // 等待1秒
        return await this.getResult(promptId);
      }
      
      return response.data;
    } catch (error) {
      console.error('从云实例获取结果时出错:', promptId);
      throw new Error(`从云实例获取结果失败: ${error.message}`);
    }
  }


  getInstanceId(){
    return this.instanceId
  }
  getUrl(){
    return this.baseURL
  }

  // 在类销毁时关闭WebSocket连接
  destroy() {
    if (this.ws) {
      this.ws.close();
    }
  }
}

module.exports = ComfyClient;
