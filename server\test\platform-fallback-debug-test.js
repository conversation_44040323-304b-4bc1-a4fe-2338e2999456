/**
 * 平台切换调试测试
 * 用于调试ComfyUI失败后切换到RunningHub也失败的情况
 */

console.log('开始调试平台切换失败问题...\n');

/**
 * 测试场景1: 模拟ComfyUI失败，RunningHub也失败的情况
 */
function testBothPlatformsFail() {
  console.log('=== 测试场景1: 模拟ComfyUI失败，RunningHub也失败 ===');
  
  try {
    // 模拟ComfyUI执行失败
    console.log('步骤1: ComfyUI执行失败');
    const comfyUIError = new Error('创建ComfyClient失败: ComfyUI服务器初始化失败，超过最大重试次数（1）');
    console.log('ComfyUI错误:', comfyUIError.message);
    
    // 检查是否是ComfyUI不可用错误
    const isComfyUIUnavailable = comfyUIError.message.includes('ComfyUI服务器初始化失败') ||
                                 comfyUIError.message.includes('创建ComfyClient失败') ||
                                 comfyUIError.message.includes('没有可用的AI实例') ||
                                 comfyUIError.message.includes('服务器健康检查失败');
    
    if (isComfyUIUnavailable) {
      console.log('✅ 检测到ComfyUI不可用，尝试切换到RunningHub...');
      
      // 模拟检查工作流支持
      const workflowName = 'A01-trending';
      const workflowSupportsRunningHub = true; // 假设支持
      
      if (workflowSupportsRunningHub) {
        console.log(`✅ 工作流 ${workflowName} 支持RunningHub平台`);
        
        try {
          // 模拟RunningHub执行也失败
          console.log('步骤2: 尝试在RunningHub执行...');
          const runningHubError = new Error('RunningHub配置不可用或执行失败');
          throw runningHubError;
          
        } catch (runningHubError) {
          console.log('❌ RunningHub执行也失败:', runningHubError.message);
          
          // 这里应该返回一个明确的错误信息，而不是抛出异常
          const finalError = `ComfyUI不可用且RunningHub执行失败: ${runningHubError.message}`;
          console.log('最终错误信息:', finalError);
          
          // 分析可能的原因
          console.log('\n可能的失败原因分析:');
          console.log('1. ComfyUI服务器启动失败或健康检查失败');
          console.log('2. RunningHub配置不可用或达到并发限制');
          console.log('3. RunningHub API调用失败');
          console.log('4. 网络连接问题');
          console.log('5. 工作流参数转换失败');
          
          return false;
        }
      } else {
        console.log(`❌ 工作流 ${workflowName} 不支持RunningHub平台`);
        return false;
      }
    } else {
      console.log('❌ 不是ComfyUI不可用错误');
      return false;
    }
    
  } catch (error) {
    console.error('测试场景1失败:', error);
    return false;
  }
}

/**
 * 测试场景2: 分析错误处理流程
 */
function testErrorHandlingFlow() {
  console.log('\n=== 测试场景2: 分析错误处理流程 ===');
  
  try {
    console.log('当前的错误处理流程:');
    console.log('1. 路由层调用 unifiedWorkflowService.executeWorkflow()');
    console.log('2. UnifiedWorkflowService 选择平台 (通常是ComfyUI)');
    console.log('3. 调用 executeOnComfyUI()');
    console.log('4. ComfyUI执行失败，检测到不可用错误');
    console.log('5. 在executeOnComfyUI()中尝试切换到RunningHub');
    console.log('6. 调用 executeOnRunningHub()');
    console.log('7. RunningHub执行也失败');
    console.log('8. 抛出错误: "ComfyUI不可用且RunningHub执行失败"');
    console.log('9. 路由层接收到错误并返回给客户端');
    
    console.log('\n问题分析:');
    console.log('- 错误处理逻辑是正确的');
    console.log('- 但是RunningHub执行失败的原因需要进一步调查');
    console.log('- 可能是RunningHub配置问题或API调用失败');
    
    console.log('\n建议的改进:');
    console.log('1. 增加更详细的RunningHub错误日志');
    console.log('2. 检查RunningHub配置的可用性');
    console.log('3. 添加RunningHub连接测试');
    console.log('4. 考虑添加更多的后备平台');
    
    return true;
    
  } catch (error) {
    console.error('测试场景2失败:', error);
    return false;
  }
}

/**
 * 测试场景3: 模拟成功的平台切换
 */
function testSuccessfulPlatformSwitch() {
  console.log('\n=== 测试场景3: 模拟成功的平台切换 ===');
  
  try {
    console.log('理想的成功切换流程:');
    
    // 步骤1: ComfyUI失败
    console.log('步骤1: ComfyUI执行失败');
    console.log('- 错误: ComfyUI服务器初始化失败');
    console.log('- 释放ComfyUI实例缓存');
    
    // 步骤2: 检测错误类型
    console.log('步骤2: 检测到ComfyUI不可用错误');
    console.log('- 错误类型匹配成功');
    console.log('- 工作流支持RunningHub平台');
    
    // 步骤3: 切换到RunningHub
    console.log('步骤3: 切换到RunningHub平台');
    console.log('- 选择可用的RunningHub配置');
    console.log('- 转换工作流参数');
    console.log('- 调用RunningHub API');
    
    // 步骤4: RunningHub执行成功
    console.log('步骤4: RunningHub执行成功');
    console.log('- 返回执行结果');
    console.log('- 平台标记为 "runninghub"');
    console.log('- 任务状态更新为处理中');
    
    console.log('✅ 成功的平台切换流程模拟完成');
    return true;
    
  } catch (error) {
    console.error('测试场景3失败:', error);
    return false;
  }
}

/**
 * 测试场景4: 检查缓存清理逻辑
 */
function testCacheCleanupLogic() {
  console.log('\n=== 测试场景4: 检查缓存清理逻辑 ===');
  
  try {
    console.log('缓存清理检查点:');
    
    // 检查点1: ComfyClientFactory中的错误处理
    console.log('1. ComfyClientFactory.createClient() 失败时:');
    console.log('   - 删除 "open_starting_" + instanceId');
    console.log('   - 调用 finishUse(instanceId) 清理使用中缓存');
    console.log('   ✅ 已实现');
    
    // 检查点2: 路由层的错误处理
    console.log('2. 路由层检测到ComfyUI错误时:');
    console.log('   - 检查 executeResult.instanceId');
    console.log('   - 调用 ComfyClientFactory.finishUse(instanceId)');
    console.log('   ❌ 已移除 (简化了错误处理)');
    
    // 检查点3: UnifiedWorkflowService中的错误处理
    console.log('3. UnifiedWorkflowService.executeOnComfyUI() 失败时:');
    console.log('   - 尝试从错误信息中提取实例ID');
    console.log('   - 调用 ComfyClientFactory.finishUse(instanceId)');
    console.log('   ✅ 已实现');
    
    console.log('\n缓存清理状态: 基本完整');
    console.log('主要的缓存清理逻辑在ComfyClientFactory中，这是最重要的清理点');
    
    return true;
    
  } catch (error) {
    console.error('测试场景4失败:', error);
    return false;
  }
}

/**
 * 测试场景5: HTTP状态码分析
 */
function testHttpStatusAnalysis() {
  console.log('\n=== 测试场景5: HTTP状态码分析 ===');
  
  try {
    console.log('HTTP状态码分析:');
    console.log('- 错误日志显示: ApiError 500');
    console.log('- 但HTTP响应显示: 200 OK');
    console.log('- 这表明错误被捕获并处理了，但可能返回了错误的状态码');
    
    console.log('\n可能的原因:');
    console.log('1. 错误被try-catch捕获，但仍然返回了200状态码');
    console.log('2. 异步错误处理导致状态码不一致');
    console.log('3. 中间件或错误处理器修改了响应状态');
    
    console.log('\n建议检查:');
    console.log('1. 路由层的错误处理是否正确抛出错误');
    console.log('2. Express错误中间件是否正确设置状态码');
    console.log('3. 异步操作的错误处理是否完整');
    
    return true;
    
  } catch (error) {
    console.error('测试场景5失败:', error);
    return false;
  }
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('平台切换调试测试\n');
  
  const results = [];
  
  // 运行各个测试场景
  results.push(testBothPlatformsFail());
  results.push(testErrorHandlingFlow());
  results.push(testSuccessfulPlatformSwitch());
  results.push(testCacheCleanupLogic());
  results.push(testHttpStatusAnalysis());
  
  console.log('\n=== 调试总结 ===');
  console.log(`总共运行了 ${results.length} 个调试场景`);
  
  const successCount = results.filter(result => result === true).length;
  console.log(`完成: ${successCount}/${results.length}`);
  
  console.log('\n=== 关键发现 ===');
  console.log('1. 错误处理逻辑基本正确');
  console.log('2. 缓存清理机制已实现');
  console.log('3. 问题可能在于RunningHub平台的执行失败');
  console.log('4. HTTP状态码不一致需要进一步调查');
  
  console.log('\n=== 下一步行动 ===');
  console.log('1. 检查RunningHub配置和API调用');
  console.log('2. 增加更详细的RunningHub错误日志');
  console.log('3. 验证工作流参数转换是否正确');
  console.log('4. 检查网络连接和API端点');
  
  return successCount === results.length;
}

// 运行调试测试
const testResult = runAllTests();

console.log('\n调试测试完成！');
process.exit(testResult ? 0 : 1);
