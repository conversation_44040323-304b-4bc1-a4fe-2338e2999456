# 新的平台优先级策略

## 概述

根据用户需求，我们实现了新的任务执行优先级策略，确保系统能够智能地选择最合适的执行平台。

## 优先级策略

### 实例状态说明
- **运行中实例（状态300）**：
  - `instance_in_use_` = true：实例正在运行任务（繁忙）
  - `instance_finished_` = true 或 `instance_in_use_` = false：实例已完成任务（空闲）
- **关机实例（状态800）**：实例已关机，但可以通过开机API启动使用

### 1. 第一优先级：ComfyUI 空闲实例
- **条件**：有空闲的运行中 ComfyUI 实例（状态300且 `instance_finished_=true` 或 `instance_in_use_=false`）
- **行为**：直接使用空闲的 ComfyUI 实例执行任务
- **原因**：空闲实例可以立即使用，无需等待

### 2. 第二优先级：启动关机的 ComfyUI 实例
- **条件**：没有空闲的运行中实例，但有关机的实例（状态800）
- **行为**：
  - 随机选择一个关机的实例
  - 调用启动API启动实例
  - 等待实例就绪（最多2分钟超时）
  - 启动成功后使用该实例执行任务
- **重要说明**：关机的实例是可用资源，通过自动启动来使用
- **失败处理**：如果启动失败，进入下一优先级

### 3. 第三优先级：RunningHub 可用配置
- **条件**：ComfyUI 实例不可用或启动失败
- **行为**：
  - 检查所有启用的 RunningHub 配置
  - 筛选支持当前工作流的配置
  - 检查每个配置的当前运行任务数
  - 选择运行任务数少于10个的配置
  - 按使用次数升序选择最少使用的配置
- **限制**：每个 RunningHub 配置最多同时运行10个任务

### 4. 第四优先级：ComfyUI 可用资源
- **条件**：所有 RunningHub 配置都已达到并发限制，但仍有 ComfyUI 可用资源
- **行为**：回到 ComfyUI 实例中执行
- **包括的可用资源**：
  - 繁忙的运行中实例（排队等待任务完成）
  - 关机的实例（可通过开机API启动使用）
- **原因**：确保任务能够执行，充分利用所有可用的 ComfyUI 资源

### 5. 最后后备：强制使用 RunningHub
- **条件**：没有任何 ComfyUI 实例可用
- **行为**：忽略并发限制，强制使用 RunningHub 配置
- **标记**：在任务记录中标记为 `forcedSelection: true`

## 实现细节

### 核心方法

#### `selectPlatformWithPriority(workflowName, context)`
- 实现新的优先级选择逻辑
- 返回详细的平台选择信息，包括选择原因

#### `tryStartComfyUIInstance(instanceService)`
- 尝试启动关机的 ComfyUI 实例
- 处理启动失败的情况
- 返回启动结果和实例信息

#### `selectAvailableRunningHubConfig(workflowName, userId, ignoreLimit)`
- 智能选择 RunningHub 配置
- 检查并发限制（10个任务/配置）
- 支持忽略限制的强制选择

### 任务记录增强

新的平台选择信息会记录到任务中：

```javascript
platformInfo: {
  selectedAt: new Date(),
  reason: "选择原因",
  selectionMethod: "priority_based",
  startedInstance: "启动的实例ID（如果有）",
  fallbackToBusy: true, // 是否回退到繁忙实例
  forcedSelection: true, // 是否强制选择
  runningHubConfig: {
    configId: "配置ID",
    configName: "配置名称",
    workflowId: "工作流ID",
    usageCount: 使用次数
  },
  configsUsage: [
    {
      name: "配置名称",
      currentRunningTasks: 当前运行任务数,
      totalTasks: 总任务数,
      selected: true // 是否被选中
    }
  ]
}
```

## 优势

1. **资源优化**：优先使用可用资源，避免不必要的等待
2. **自动恢复**：能够自动启动关机的实例，提高资源利用率
3. **负载均衡**：RunningHub 配置按使用次数和并发数智能分配
4. **容错性强**：多层后备方案确保任务能够执行
5. **详细记录**：完整记录选择过程，便于监控和调试

## 监控和调试

- 所有平台选择决策都会记录详细日志
- 任务记录中包含完整的选择信息
- 支持通过任务记录分析平台使用情况
- 可以监控 ComfyUI 实例启动成功率
- 可以监控 RunningHub 配置的并发使用情况

## 配置要求

- ComfyUI 实例需要支持启动/停止 API
- RunningHub 配置需要正确设置工作流映射
- 数据库中需要有 FlowTask 模型来记录任务状态
- 需要正确配置实例状态缓存系统
