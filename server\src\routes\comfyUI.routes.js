const express = require('express');

const FlowTask = require('../models/FlowTask');

const router = express.Router();
const multer = require('multer');

const path = require('path');
const fs = require('fs');

const { auth } = require('../modules/auth/auth.middleware');
const ComfyClient = require('../services/comfyClient/client');
const ComfyClientFactory = require('../services/comfyClient/ComfyClientFactory');
const MessageProcessor = require('../services/comfyClient/core/MessageProcessor');
const WorkflowManager = require('../services/comfyClient/core/WorkflowManager');
const WorkflowService = require('../services/comfyClient/service/WorkflowService');
const CreditService = require('../services/credits/creditService');
const UnifiedWorkflowService = require('../services/unified/UnifiedWorkflowService');
const { createError } = require('../utils/error');

const Config = require('config');

const { formatTokenToMD5 } = require('../utils/tokenUtils');

// 创建服务实例
const comfyClient = new ComfyClient();
const workflowManager = new WorkflowManager();
const messageProcessor = new MessageProcessor(workflowManager);
const workflowService = new WorkflowService(workflowManager,  messageProcessor);

// 创建统一工作流服务实例
const unifiedWorkflowService = new UnifiedWorkflowService();
unifiedWorkflowService.setComfyWorkflowService(workflowService);

// 创建临时上传目录
const uploadDir = path.join(__dirname, '../../uploads/temp');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置multer存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 限制10MB
  },
  fileFilter: (req, file, cb) => {
    // 只允许图片文件
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传图片文件'));
    }
  }
});

// 检查ComfyUI服务状态
router.get('/status', auth, async (req, res, next) => {
  try {
    const status = await comfyClient.checkService();
    res.json({
      success: true,
      data: {
        status: status ? 'online' : 'offline',
        baseURL: comfyClient.baseURL,
        wsURL: comfyClient.wsURL,
        apiPathPrefix: comfyClient.apiPathPrefix
      }
    });
  } catch (error) {
    next(createError(500, `检查ComfyUI服务状态失败: ${error.message}`));
  }
});

// 执行工作流
router.post('/execute', auth, async (req, res, next) => {
  try {
    const { workflow, params } = req.body;

    if (!workflow || !workflow.prompt) {
      throw createError(400, '无效的工作流配置');
    }

    // 执行工作流
    const result = await comfyClient.executeWorkflow(workflow, params);

    res.json({
      success: true,
      data: {
        ...result,
        message: '工作流执行已启动'
      }
    });
  } catch (error) {
    next(createError(500, `执行工作流失败: ${error.message}`));
  }
});

router.post('/execute2', auth, async (req, res, next) => {
    const {workflowName, taskId} = req.query;
    const params = req.body;
    try {
        const taskData = await FlowTask.findOne({taskId});
        if (!taskData) {
            console.log('任务不存在:',taskId);
            throw createError(404, '任务不存在');
        }

        // 更新任务状态为处理中
        await FlowTask.findOneAndUpdate(
            { taskId },
            { status: 'processing' },
            { new: true }
        );

        const callback = async (promptId, instanceId,url) => {
            // 更新任务状态和相关ID
            await FlowTask.findOneAndUpdate(
                { taskId },
                {
                    promptId,
                    instanceId,
                    url,
                    status: 'processing'
                },
                { new: true }
            );
        };
        params.token = formatTokenToMD5(req.token);

        // 构建用户上下文
        const userContext = {
            isVip: req.user.isVip || false,
            isPaid: req.user.isPaid || false,
            membershipLevel: req.user.membershipLevel || 'free',
            credits: req.user.credits || 0
        };

        // 检查用户是否已经在ComfyUI平台执行任务，如果有则强制使用ComfyUI平台
        const instanceService = require('../services/instance/instanceService');
        const deviceToken = formatTokenToMD5(req.token);

        // 查询用户当前是否有正在运行的ComfyUI任务
        const runningComfyUITasks = await FlowTask.find({
            deviceToken: deviceToken,
            status: { $in: ['pending', 'processing'] },
            platform: 'comfyui',
            instanceId: { $exists: true, $ne: null }
        }).sort({ createdAt: -1 }).limit(1);

        // 如果用户已经在ComfyUI平台执行任务，强制使用ComfyUI平台并指定实例
        if (runningComfyUITasks.length > 0) {
            const runningTask = runningComfyUITasks[0];
            console.log(`用户 ${req.user._id} 当前正在ComfyUI实例 ${runningTask.instanceId} 上执行任务 ${runningTask.taskId}，新任务将继续在该实例上执行`);

            // 强制指定平台为ComfyUI
            params.forcePlatform = 'comfyui';

            // 将用户当前运行的实例信息传递给上下文
            userContext.preferredInstanceId = runningTask.instanceId;
            userContext.continuityReason = `用户当前正在实例 ${runningTask.instanceId} 上执行任务 ${runningTask.taskId}`;
        }

        // 使用统一工作流服务执行
        const executeResult = await unifiedWorkflowService.executeWorkflow(
            workflowName,
            params,
            req.user._id,
            taskId,
            callback,
            userContext
        );

        if (!executeResult.success) {
            throw createError(500, executeResult.error);
        }
        executeResult.userId = req.user._id;
        executeResult.count = params.subInfo.count;

        // 开始异步处理结果
        processWorkflowResultAsync(
            executeResult.platform,
            executeResult.promptId,
            executeResult.instanceId,
            workflowName,
            taskId,
            executeResult.url,
            executeResult.client,
            executeResult
        );
        // 判断是否是dev
        if(process.env.NODE_ENV === 'development'){
            // 如果是dev，则立即返回执行状态
            res.json({
                success: true,
                data: {
                  ...executeResult,
                  populatedWorkflow:null,
                    client:null,
                    promptTipsData:null,
                    runningHubService:null
                }
            });
        }else{

        // 立即返回执行状态
        res.json({
            success: true,
            data: {
                ...executeResult,
                populatedWorkflow:null,
                client:null,
                promptTipsData:null,
                runningHubService:null
            }
        });
        }

    } catch(error) {
        await FlowTask.findOneAndUpdate(
            { taskId },
            {
                status: 'failed',
                error: error.message
            }
        );
        console.log('执行人物流失败:',error);
        // 返还用户的算力
        await CreditService.refundCredits(req.user._id, taskId);
        res.json({
            success: false,
            message: `工作流执行失败:${error}`
        });
    }
});

// 添加异步处理结果的函数
async function processWorkflowResultAsync(platform, promptId, instanceId, workflowName, taskId, url, client, executeResult) {
    try {
        // 使用统一工作流服务处理结果
        const result = await unifiedWorkflowService.processWorkflowResult(
            platform,
            promptId,
            instanceId,
            workflowName,
            taskId,
            url,
            client,
            executeResult
        );

        if (!result.success) {
            throw new Error(result.error);
        }

        // 准备更新数据
        const updateData = {
            status: 'completed',
            platform: platform,
            completedAt: new Date()
        };

        // 根据工作流类型处理不同的结果
        if (workflowName === 'C01-extract') {
            updateData.extractedText = result.data.outputs.prompt;
        } else {
            // 处理图片结果
            const processedImages = result.data.outputs.images.map((image, index) => ({
                ...image,
                imageIndex: index,
                fileInfo: { ...image }
            }));

            updateData.processInfo = {
                results: processedImages
            };

            updateData.generatedImages = processedImages.map((image, index) => ({
                imageIndex: index,
                status: 'completed',
                url: image.ossUrl || image.url,
                ...image,
                fileInfo: { ...image }
            }));
        }

        // 更新任务状态和结果
        await FlowTask.findOneAndUpdate(
            { taskId },
            updateData,
            { new: true }
        );

        console.log(`任务 ${taskId} 在平台 ${platform} 上执行完成`);

    } catch (error) {
        console.error('处理工作流结果失败:', error);
        await FlowTask.findOneAndUpdate(
            { taskId },
            {
                status: 'failed',
                error: error.message,
                failedAt: new Date()
            }
        );

        // 只有ComfyUI平台需要释放实例
        if (platform === 'comfyui' && instanceId !== 'runninghub') {
            try {
                await ComfyClientFactory.finishUse(instanceId);
            } catch (releaseError) {
                console.error('释放ComfyUI实例失败:', releaseError);
            }
        }

        // 返还用户的算力
        await CreditService.refundCredits(executeResult.userId, taskId, executeResult.count);
    }
}

// 获取工作流结果
router.get('/result/:promptId', auth, async (req, res, next) => {
  try {
    const { promptId } = req.params;

    if (!promptId) {
      throw createError(400, '未提供promptId');
    }

    // 获取结果
    const result = await comfyClient.getResult(promptId);

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    next(createError(500, `获取结果失败: ${error.message}`));
  }
});

// 等待工作流结果（长轮询）
router.get('/wait-result/:promptId', auth, async (req, res, next) => {
  try {
    const { promptId } = req.params;
    const { timeout } = req.query;

    if (!promptId) {
      throw createError(400, '未提供promptId');
    }

    // 设置超时时间，默认5分钟
    const timeoutMs = parseInt(timeout) || 300000;

    // 等待结果
    const result = await comfyClient.waitForResult(promptId, timeoutMs);

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    next(createError(500, `等待结果失败: ${error.message}`));
  }
});

// 一站式工作流执行（上传+执行+等待结果）
router.post('/process', auth, upload.single('image'), async (req, res, next) => {
  try {
    if (!req.file) {
      throw createError(400, '未提供图片文件');
    }

    // 解析工作流和参数
    const workflow = JSON.parse(req.body.workflow || '{}');
    const additionalParams = JSON.parse(req.body.params || '{}');

    if (!workflow || !workflow.prompt) {
      throw createError(400, '无效的工作流配置');
    }

    // 上传图片
    const uploadResult = await comfyClient.uploadImage(req.file.path);

    // 删除临时文件
    fs.unlinkSync(req.file.path);

    // 准备参数
    const params = {
      image: uploadResult.url,
      additionalParams
    };

    // 执行工作流
    const executeResult = await comfyClient.executeWorkflow(workflow, params);

    // 等待结果
    const result = await comfyClient.waitForResult(executeResult.prompt_id);

    res.json({
      success: true,
      data: {
        upload: uploadResult,
        execution: executeResult,
        result
      }
    });
  } catch (error) {
    // 确保清理临时文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    next(createError(500, `处理失败: ${error.message}`));
  }
});

// 获取预设工作流列表
router.get('/workflows', auth, (req, res) => {
  try {
    // 从配置中获取预设工作流列表
    const workflows = Config.get('comfyUi.workflows') || [];

    res.json({
      success: true,
      data: {
        workflows: workflows.map(w => ({
          id: w.id,
          name: w.name,
          description: w.description,
          category: w.category
        }))
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: `获取工作流列表失败: ${error.message}`
    });
  }
});

// 获取特定预设工作流
router.get('/workflows/:id', auth, (req, res) => {
  try {
    const { id } = req.params;

    // 从配置中获取预设工作流
    const workflows = Config.get('comfyUi.workflows') || [];
    const workflow = workflows.find(w => w.id === id);

    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: `未找到ID为${id}的工作流`
      });
    }

    res.json({
      success: true,
      data: workflow
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: `获取工作流失败: ${error.message}`
    });
  }
});

// 获取平台状态
router.get('/platform-status', auth, async (req, res) => {
  try {
    const { getPlatformStatus } = require('../config/platformConfig');
    const status = await getPlatformStatus();

    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    console.error('获取平台状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取工作流平台推荐
router.post('/platform-recommendation', auth, async (req, res) => {
  try {
    const { workflowName } = req.body;
    const { getRecommendedPlatform, getComfyUIInstancesStatus } = require('../config/platformConfig');

    // 构建上下文信息
    const context = {
      userId: req.user._id // 添加用户ID到上下文
    };

    // 获取ComfyUI实例状态
    try {
      const comfyuiInstancesStatus = await getComfyUIInstancesStatus();
      context.comfyuiInstancesStatus = comfyuiInstancesStatus;
    } catch (error) {
      console.error('获取ComfyUI实例状态失败:', error);
    }

    const recommendation = await getRecommendedPlatform(workflowName, context);

    res.json({
      success: true,
      data: {
        workflowName,
        recommendedPlatform: recommendation.platform,
        reason: recommendation.reason,
        comfyuiInstancesStatus: context.comfyuiInstancesStatus,
        configsUsage: recommendation.configsUsage,
        selectedConfig: recommendation.config ? {
          id: recommendation.config._id,
          name: recommendation.config.name,
          usageCount: recommendation.config.usage.totalTasks || 0
        } : null
      }
    });
  } catch (error) {
    console.error('获取平台推荐失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
