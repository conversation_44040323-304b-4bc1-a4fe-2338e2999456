/**
 * 用户实例连续性功能测试
 * 测试工作流执行前检查用户是否已经在ComfyUI平台执行任务，
 * 如果有则继续在原本的实例上执行
 */

console.log('开始测试用户实例连续性功能...\n');

// 模拟数据
const mockUserId = 'test_user_123';
const mockDeviceToken = 'device_token_456';
const mockInstanceId1 = 'instance_001';
const mockInstanceId2 = 'instance_002';
const mockTaskId1 = 'TASK001';
const mockTaskId2 = 'TASK002';

/**
 * 测试场景1: 用户当前没有正在运行的ComfyUI任务
 */
function testNoRunningTasks() {
  console.log('=== 测试场景1: 用户当前没有正在运行的ComfyUI任务 ===');
  
  try {
    // 模拟查询结果：没有正在运行的任务
    const runningComfyUITasks = [];
    
    console.log('查询用户当前正在运行的ComfyUI任务...');
    console.log('查询结果:', runningComfyUITasks);
    
    if (runningComfyUITasks.length === 0) {
      console.log('✅ 用户当前没有正在运行的ComfyUI任务，将使用正常的平台选择逻辑');
      console.log('- 不会强制指定平台');
      console.log('- 不会设置优先实例ID');
      return true;
    } else {
      console.log('❌ 测试失败: 应该没有正在运行的任务');
      return false;
    }
    
  } catch (error) {
    console.error('测试场景1失败:', error);
    return false;
  }
}

/**
 * 测试场景2: 用户当前正在ComfyUI平台执行任务
 */
function testUserHasRunningComfyUITask() {
  console.log('\n=== 测试场景2: 用户当前正在ComfyUI平台执行任务 ===');
  
  try {
    // 模拟查询结果：用户有正在运行的ComfyUI任务
    const runningComfyUITasks = [
      {
        taskId: mockTaskId1,
        deviceToken: mockDeviceToken,
        status: 'processing',
        platform: 'comfyui',
        instanceId: mockInstanceId1,
        createdAt: new Date()
      }
    ];
    
    console.log('查询用户当前正在运行的ComfyUI任务...');
    console.log('查询结果:', runningComfyUITasks.length, '个任务');
    
    if (runningComfyUITasks.length > 0) {
      const runningTask = runningComfyUITasks[0];
      console.log(`✅ 发现用户正在ComfyUI实例 ${runningTask.instanceId} 上执行任务 ${runningTask.taskId}`);
      
      // 模拟设置强制平台和优先实例
      const params = { forcePlatform: 'comfyui' };
      const userContext = {
        preferredInstanceId: runningTask.instanceId,
        continuityReason: `用户当前正在实例 ${runningTask.instanceId} 上执行任务 ${runningTask.taskId}`
      };
      
      console.log('- 强制指定平台:', params.forcePlatform);
      console.log('- 优先实例ID:', userContext.preferredInstanceId);
      console.log('- 连续性原因:', userContext.continuityReason);
      
      return true;
    } else {
      console.log('❌ 测试失败: 应该有正在运行的任务');
      return false;
    }
    
  } catch (error) {
    console.error('测试场景2失败:', error);
    return false;
  }
}

/**
 * 测试场景3: 用户有多个正在运行的ComfyUI任务
 */
function testUserHasMultipleRunningTasks() {
  console.log('\n=== 测试场景3: 用户有多个正在运行的ComfyUI任务 ===');
  
  try {
    // 模拟查询结果：用户有多个正在运行的ComfyUI任务
    const runningComfyUITasks = [
      {
        taskId: mockTaskId1,
        deviceToken: mockDeviceToken,
        status: 'processing',
        platform: 'comfyui',
        instanceId: mockInstanceId1,
        createdAt: new Date(Date.now() - 5 * 60 * 1000) // 5分钟前
      },
      {
        taskId: mockTaskId2,
        deviceToken: mockDeviceToken,
        status: 'pending',
        platform: 'comfyui',
        instanceId: mockInstanceId2,
        createdAt: new Date() // 刚刚创建
      }
    ];
    
    console.log('查询用户当前正在运行的ComfyUI任务...');
    console.log('查询结果:', runningComfyUITasks.length, '个任务');
    
    if (runningComfyUITasks.length > 0) {
      // 按创建时间倒序排序，选择最新的任务
      const sortedTasks = runningComfyUITasks.sort((a, b) => b.createdAt - a.createdAt);
      const latestTask = sortedTasks[0];
      
      console.log(`✅ 发现用户有 ${runningComfyUITasks.length} 个正在运行的ComfyUI任务`);
      console.log(`- 选择最新的任务: ${latestTask.taskId} (实例: ${latestTask.instanceId})`);
      
      // 模拟设置强制平台和优先实例
      const params = { forcePlatform: 'comfyui' };
      const userContext = {
        preferredInstanceId: latestTask.instanceId,
        continuityReason: `用户当前正在实例 ${latestTask.instanceId} 上执行任务 ${latestTask.taskId}`
      };
      
      console.log('- 强制指定平台:', params.forcePlatform);
      console.log('- 优先实例ID:', userContext.preferredInstanceId);
      
      return true;
    } else {
      console.log('❌ 测试失败: 应该有正在运行的任务');
      return false;
    }
    
  } catch (error) {
    console.error('测试场景3失败:', error);
    return false;
  }
}

/**
 * 测试场景4: 实例选择逻辑验证
 */
function testInstanceSelectionLogic() {
  console.log('\n=== 测试场景4: 实例选择逻辑验证 ===');
  
  try {
    const preferredInstanceId = mockInstanceId1;
    const availableInstances = [
      { instanceId: mockInstanceId1, name: '实例1', status: 300 },
      { instanceId: mockInstanceId2, name: '实例2', status: 300 }
    ];
    
    console.log('模拟实例选择逻辑...');
    console.log('- 优先实例ID:', preferredInstanceId);
    console.log('- 可用实例列表:', availableInstances.map(i => i.instanceId));
    
    // 查找优先实例
    const preferredInstance = availableInstances.find(
      instance => instance.instanceId === preferredInstanceId
    );
    
    if (preferredInstance) {
      console.log(`✅ 找到优先实例: ${preferredInstance.instanceId}`);
      
      // 模拟检查实例状态
      if (preferredInstance.status === 300) {
        console.log('- 实例状态正常 (300)');
        console.log('- 可以继续在该实例上执行任务');
        return true;
      } else {
        console.log('- 实例状态异常，需要选择其他实例');
        return false;
      }
    } else {
      console.log('❌ 优先实例不在可用列表中');
      return false;
    }
    
  } catch (error) {
    console.error('测试场景4失败:', error);
    return false;
  }
}

/**
 * 测试场景5: 完整的工作流程模拟
 */
function testCompleteWorkflow() {
  console.log('\n=== 测试场景5: 完整的工作流程模拟 ===');
  
  try {
    console.log('模拟完整的用户实例连续性工作流程...');
    
    // 步骤1: 检查用户当前运行状态
    console.log('步骤1: 检查用户当前运行状态');
    const runningComfyUITasks = [
      {
        taskId: mockTaskId1,
        deviceToken: mockDeviceToken,
        status: 'processing',
        platform: 'comfyui',
        instanceId: mockInstanceId1,
        createdAt: new Date()
      }
    ];
    
    // 步骤2: 发现正在运行的任务
    if (runningComfyUITasks.length > 0) {
      const runningTask = runningComfyUITasks[0];
      console.log(`步骤2: 发现用户正在实例 ${runningTask.instanceId} 上执行任务`);
      
      // 步骤3: 设置强制平台和优先实例
      console.log('步骤3: 设置强制平台和优先实例');
      const params = { forcePlatform: 'comfyui' };
      const userContext = {
        preferredInstanceId: runningTask.instanceId,
        continuityReason: `用户当前正在实例 ${runningTask.instanceId} 上执行任务 ${runningTask.taskId}`
      };
      
      // 步骤4: 平台选择（会选择ComfyUI）
      console.log('步骤4: 平台选择 - 强制使用ComfyUI平台');
      
      // 步骤5: 实例选择（会优先选择指定实例）
      console.log('步骤5: 实例选择 - 优先选择指定实例');
      console.log(`- 优先实例ID: ${userContext.preferredInstanceId}`);
      
      // 步骤6: 执行工作流
      console.log('步骤6: 在指定实例上执行工作流');
      
      console.log('✅ 完整工作流程测试通过');
      console.log('- 用户任务将在同一实例上连续执行');
      console.log('- 实现了实例连续性目标');
      
      return true;
    } else {
      console.log('步骤2: 用户当前没有正在运行的任务，使用正常流程');
      return true;
    }
    
  } catch (error) {
    console.error('测试场景5失败:', error);
    return false;
  }
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('用户实例连续性功能测试\n');
  
  const results = [];
  
  // 运行各个测试场景
  results.push(testNoRunningTasks());
  results.push(testUserHasRunningComfyUITask());
  results.push(testUserHasMultipleRunningTasks());
  results.push(testInstanceSelectionLogic());
  results.push(testCompleteWorkflow());
  
  console.log('\n=== 测试总结 ===');
  console.log(`总共运行了 ${results.length} 个测试场景`);
  
  const successCount = results.filter(result => result === true).length;
  console.log(`成功: ${successCount}/${results.length}`);
  
  if (successCount === results.length) {
    console.log('🎉 所有测试通过！');
  } else {
    console.log('⚠️  部分测试失败，请检查实现');
  }
  
  return successCount === results.length;
}

// 运行测试
const testResult = runAllTests();

console.log('\n=== 功能说明 ===');
console.log('1. 在工作流执行前，系统会检查用户是否已经在ComfyUI平台执行任务');
console.log('2. 如果用户有正在运行的ComfyUI任务，新任务会强制使用ComfyUI平台');
console.log('3. 系统会将用户当前运行的实例ID作为优先实例传递给实例选择逻辑');
console.log('4. 实例选择时会优先选择用户当前正在使用的实例');
console.log('5. 这样确保了用户的任务在同一个ComfyUI实例上连续执行');

console.log('\n测试完成！');
process.exit(testResult ? 0 : 1);
