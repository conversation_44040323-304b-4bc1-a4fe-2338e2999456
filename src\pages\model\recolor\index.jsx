import React, { useRef, useEffect, useState, useCallback, Suspense, memo } from 'react';
import './index.css';
import { MdOutlineZoomOutMap, MdOutlineAutoAwesome, MdClose, MdOutlineDescription } from 'react-icons/md';
import Masonry from 'masonry-layout';
import { Modal, Button, Spin, message, Input, Tabs } from 'antd';
import 'antd/dist/reset.css';
// import { filterShowcaseByTag } from '../../../config/showcase/showcase'; // This is no longer needed
import { UPLOAD_CONFIG } from '../../../config/uploads/upload';
import { getModelImagePath } from '../../../data/models';
import { generateId, ID_TYPES } from '../../../utils/idGenerator';
import UploadGuideModal from '../../../components/UploadGuideModal';
import ImageInfoModal from '../../../components/ImageInfoModal';
import ImageDetailsModal from '../../../components/ImageDetailsModal';
import UploadBox from '../../../components/UploadBox';
import ClothingPanel from '../../../components/ClothingPanel';
import ColorPanel from '../../../components/ColorPanel';
import ColorPickerModal from '../../../components/ColorPickerModal';
import TaskPanel from '../../../components/TaskPanel';
import PromptIfUnsaved from '../../../components/PromptIfUnsaved';
import Showcase from '../../../components/Showcase';
import ControlPanel from '../../../components/ControlPanel';
import ResizeHandle from '../../../components/ResizeHandle';
import GenerationArea from '../../../components/GenerationArea';
import RequireLogin from '../../../components/RequireLogin';
import request from '../../../api/request';
import ImageZoomControl from '../../../components/ImageZoomControl';
import ImageNavigator from '../../../components/ImageNavigator';
import ThumbnailList from '../../../components/ThumbnailList';
import { getCurrentUserId } from '../../../api';
import ColorAdjustPanel from '../../../components/ColorAdjustPanel';
import TypeSelector from '../../../components/TypeSelector';
import TipsPanel from '../../../components/TipsPanel';
import { showDeleteConfirmModal } from '../../../utils/modalUtils';
import JSZip from 'jszip';
import { getTaskComponent } from '../../../utils/taskAdapters';
import MaskDescriptionPanel from '../../../components/MaskDescriptionPanel';
import { uploadImage } from '../../../api/upload'; // 导入图片上传API函数
import { createFlowTask, updateFlowTask, getFlowTasks, getFlowTaskDetail, deleteFlowTask,checkUserBalance } from '../../../api/flowtask';
import { WORKFLOW_NAME } from '../../../data/workflowName';
import { uploadFiles } from '../../../api/ossUpload';
import { executeFlow } from '../../../api/flow';
import { useTaskContext } from '../../../contexts/TaskContext';

// 定义默认调整值
const DEFAULT_VALUES = {
  hue: 0,         // -180 to 180
  saturation: 0,  // -1.00 to 1.00
  brightness: 0,  // -1.00 to 1.00
  contrast: 0     // -1.00 to 1.00
};

// 颜色转换辅助函数
// 转换RGB到HSL
const toHsl = (r, g, b) => {
  r /= 255;
  g /= 255;
  b /= 255;
  
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h, s, l = (max + min) / 2;
  
  if (max === min) {
    h = s = 0;
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
      default: break;
    }
    
    h /= 6;
  }
  
  return { h: h * 360, s: s * 100, l: l * 100 };
};

// 从HSL转回RGB
const fromHsl = (h, s, l) => {
  h /= 360;
  s /= 100;
  l /= 100;
  
  let r, g, b;
  
  if (s === 0) {
    r = g = b = l;
  } else {
    const hue2rgb = (p, q, t) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1/6) return p + (q - p) * 6 * t;
      if (t < 1/2) return q;
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
      return p;
    };
    
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    
    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }
  
  return {
    r: Math.round(r * 255),
    g: Math.round(g * 255),
    b: Math.round(b * 255)
  };
};

// 将RGB转为16进制颜色
const toHex = (r, g, b) => {
  return '#' + [r, g, b].map(x => {
    const hex = x.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  }).join('');
};

// 应用对比度调整
const applyContrast = (rgb, contrastValue) => {
  // 对比度调整的公式: 新值 = (旧值 - 中间值) * (1 + 对比度) + 中间值
  const factor = 1 + contrastValue; // 对比度因子
  const midpoint = 128; // RGB中间值
  
  // 对每个RGB通道应用对比度
  return {
    r: Math.round(Math.max(0, Math.min(255, (rgb.r - midpoint) * factor + midpoint))),
    g: Math.round(Math.max(0, Math.min(255, (rgb.g - midpoint) * factor + midpoint))),
    b: Math.round(Math.max(0, Math.min(255, (rgb.b - midpoint) * factor + midpoint)))
  };
};

const MemoizedImageDetailsModal = React.memo(ImageDetailsModal);

const RecolorPage = ({ isLoggedIn, userId }) => {
  const containerRef = useRef(null);
  const controlPanelRef = useRef(null);
  const handleRef = useRef(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const formRef = useRef(null);
  const [activeTab, setActiveTab] = useState('result');
  const [controlPanelWidth, setControlPanelWidth] = useState(28);
  const showcaseRef = useRef(null);
  const masonryRef = useRef(null);
  const [showUploadGuide, setShowUploadGuide] = useState(false);
  const [uploadGuideType, setUploadGuideType] = useState('recolorClothing');
  const [processedImages, setProcessedImages] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [clothingPanels, setClothingPanels] = useState([]);
  const [currentReuploadClothingPanelId, setCurrentReuploadClothingPanelId] = useState(null);
  const [showClothingUploadGuide, setShowClothingUploadGuide] = useState(false);
  const [operationsPanel, setOperationsPanel] = useState(null);
  const [showImageInfo, setShowImageInfo] = useState(false);
  const [currentImagePanel, setCurrentImagePanel] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageDetails, setShowImageDetails] = useState(false);
  const [imageDetailsTask, setImageDetailsTask] = useState(null);
  const [showAdvancedText, setShowAdvancedText] = useState(false);

  const [advancedPopupPosition, setAdvancedPopupPosition] = useState({ top: 0, left: 0 });
  
  // 添加缺失的状态变量
  const [selectedPreset, setSelectedPreset] = useState(null);
  const [clothingImage, setClothingImage] = useState(null);
  const [originalWidth, setOriginalWidth] = useState(0);
  const [originalHeight, setOriginalHeight] = useState(0);
  const [selectedFabricType, setSelectedFabricType] = useState(null);
  const [customWidth, setCustomWidth] = useState(1024);
  const [customHeight, setCustomHeight] = useState(1536);
  const [isEditing, setIsEditing] = useState(false);
  const [editingTask, setEditingTask] = useState(null);
  const [taskModalVisible, setTaskModalVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [imageSettings, setImageSettings] = useState(null);
  const [imageDetailsVisible, setImageDetailsVisible] = useState(false);
  
  // 添加拖动状态管理
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const lastPosition = useRef({ x: 0, y: 0 });
  
  // 添加图片缩放相关状态
  const [imageScale, setImageScale] = useState(100);
  const [initialScale, setInitialScale] = useState(100);
  const imageRef = useRef(null);
  
  // 添加加载状态
  const [isLoadingTasks, setIsLoadingTasks] = useState(false);
  
  // 添加颜色选择相关状态
  const [selectedColor, setSelectedColor] = useState(null);
  const [selectedColors, setSelectedColors] = useState([]);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [colorPickerPosition, setColorPickerPosition] = useState({ top: 0, left: 0 });
  const [colorAdjustments, setColorAdjustments] = useState({
    hue: 0,         // -180 to 180
    saturation: 0,  // -1.00 to 1.00
    brightness: 0,  // -1.00 to 1.00
    contrast: 0     // -1.00 to 1.00
  });
  const [colorPreviewData, setColorPreviewData] = useState(null);
  // 添加面料类型状态
  const [fabricType, setFabricType] = useState(2); // 默认为中性色服装，对应数字2
  const generationAreaRef = useRef(null);
  // 添加图片生成数量状态，固定为1张
  const [imageQuantity, setImageQuantity] = useState(1);
  
  // 添加TaskContext的使用
  const { updateTask } = useTaskContext();

  // 页面刷新/关闭提示
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）';
        return e.returnValue;
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);

  // 初始化和清理Masonry布局
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      // 销毁已存在的实例
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }

      // 创建新实例
      masonryRef.current = new Masonry(showcaseRef.current, {
        itemSelector: '.showcase-item',
        columnWidth: '.showcase-item',
        percentPosition: true,
        transitionDuration: '0.3s',
        initLayout: true,
        gutter: 16,
        fitWidth: false,
        horizontalOrder: true
      });

      // 监听窗口大小变化
      const handleResize = () => {
        if (masonryRef.current) {
          setTimeout(() => {
            masonryRef.current.layout();
          }, 100);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (masonryRef.current) {
          masonryRef.current.destroy();
          masonryRef.current = null;
        }
      };
    }
  }, [activeTab]);

  // 监听图片加载
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      const images = showcaseRef.current.getElementsByTagName('img');
      let loadedCount = 0;

      const handleImageLoad = () => {
        loadedCount++;
        if (masonryRef.current) {
          masonryRef.current.layout();
        }
      };

      Array.from(images).forEach(img => {
        if (img.complete) {
          handleImageLoad();
        } else {
          img.addEventListener('load', handleImageLoad);
        }
      });

      return () => {
        Array.from(images).forEach(img => {
          img.removeEventListener('load', handleImageLoad);
        });
      };
    }
  }, [activeTab]);

  // 清理临时URL资源
  useEffect(() => {
    return () => {
      // 组件卸载时释放所有临时URL
      if (clothingPanels.length > 0) {
        clothingPanels.forEach(panel => {
          if (panel.url && typeof panel.url === 'string' && panel.url.startsWith('blob:')) {
            console.log('释放临时URL资源:', panel.url);
            URL.revokeObjectURL(panel.url);
          }
        });
      }
    };
  }, [clothingPanels]);

  const handleUploadResult = (results) => {
    console.log('服装上传结果处理:', results);
    
    if (results.type === 'panels') {
      const panelsWithType = results.panels.map(panel => ({
        ...panel,
        type: 'clothing'
      }));
      setProcessedImages(prevImages => [...prevImages, ...panelsWithType]);
    } else if (results.type === 'update') {
      // 处理单个面板更新的情况
      console.log(`更新面板 ${results.panelId} 的状态:`, results.data);
      
      setProcessedImages(prevImages => 
        prevImages.map(panel => {
          if (panel.componentId === results.panelId) {
            // 根据processed数据更新面板
            if (results.data?.processed) {
              const processedData = results.data.processed;
              
              // 构建处理后图片的URL
              let processedImageUrl = null;
              if (processedData.url) {
                // 如果已经提供了构建好的URL，直接使用
                processedImageUrl = processedData.url;
                console.log('使用提供的URL:', processedImageUrl);
              } else if (processedData.relativePath) {
                processedImageUrl = `http://localhost:3002${processedData.relativePath}`;
                console.log('基于相对路径构建URL:', processedImageUrl);
              }
              
              console.log('处理后的图片URL:', processedImageUrl);
              
              // 返回更新后的面板数据
              return {
                ...panel,
                status: 'completed',  // 更新状态为已完成
                processedFile: processedImageUrl,
                fileInfo: processedData.fileInfo || panel.fileInfo,
                error: null
              };
            }
            
            // 如果没有processed数据，检查是否有错误
            if (results.error) {
              return {
                ...panel,
                status: 'error',
                error: results.error
              };
            }
            
            // 如果既没有processed数据也没有错误，保持原样
            return panel;
          }
          
          // 不是要更新的面板，保持不变
          return panel;
        })
      );
    } else if (results.type === 'error') {
      console.error('上传错误:', results.error);
      message.error('上传失败: ' + results.error);
      // 移除处理中的面板
      setProcessedImages(prevImages => 
        prevImages.filter(panel => panel.status !== 'processing'));
    }
  };

  // 处理服装图上传结果
  const handleClothingUploadResult = (results) => {
    console.log('服装复色图上传结果:', results);
    setHasUnsavedChanges(true);
    
    try {
      if (results.type === 'panels') {
        if (currentReuploadClothingPanelId) {
          // 如果是重新上传，替换原有面板
          setClothingPanels(prevPanels => 
            prevPanels.map(panel => 
              panel.componentId === currentReuploadClothingPanelId 
                ? { 
                    ...results.panels[0], 
                    componentId: currentReuploadClothingPanelId,
                    // 确保保留file对象
                    file: results.panels[0].file 
                  }
                : panel
            )
          );
          // 重置当前重新上传的面板ID
          setCurrentReuploadClothingPanelId(null);
        } else {
          // 如果是新上传，添加新面板，设置业务类型和来源
          // 确保保留原始文件对象，供后续上传使用
          const panelsWithFile = results.panels.map(panel => {
            // 检查并记录panel是否包含file属性
            if (panel.file) {
              console.log('保留服装图片原始文件对象:', panel.file.name);
              // 确保file对象正确保存，并设置合适的type和source
              return {
                ...panel,
                file: panel.file,    // 显式保留file对象
                type: 'clothing',    // 设置业务类型
                source: 'upload'     // 设置来源为用户上传
              };
            }
            return {
              ...panel,
              type: 'clothing',     // 设置业务类型
              source: 'upload'      // 设置来源为用户上传
            };
          });
          
          setClothingPanels(prevPanels => [...prevPanels, ...panelsWithFile]);
        }
      } else if (results.type === 'error') {
        console.error('上传错误:', results.error);
        message.error('上传失败: ' + results.error);
        // 移除处理中的面板
        setClothingPanels(prevPanels => 
          prevPanels.filter(panel => panel.status !== 'processing'));
        // 重置当前重新上传的面板ID
        setCurrentReuploadClothingPanelId(null);
      }
    } catch (error) {
      console.error('上传结果处理失败:', error);
      message.error('上传结果处理失败: ' + (error.message || '未知错误'));
      // 移除处理中的面板
      setClothingPanels(prevPanels => 
        prevPanels.filter(panel => panel.status !== 'processing'));
      // 重置当前重新上传的面板ID
      setCurrentReuploadClothingPanelId(null);
    }
  };

  // 处理服装图上传
  const handleClothingFileUpload = (file) => {
    console.log('服装图上传:', file);
  };
  const handleViewDetails = (image, task) => {
    try {
      // 查看任务详情日志
      console.log('查看图片详情, 任务:', task);
      console.log('当前图片:', image);
      
      // 只处理数组格式的组件，不再支持对象格式
      const components = Array.isArray(task.components) ? task.components : [];
      
      console.log('处理的组件数据:', components);
      
      // 使用小写组件名查找各个组件
      const clothingComponent = components.find(c => c.componentType === 'clothingPanel');
      const colorComponent = components.find(c => c.componentType === 'colorPanel');
      const colorAdjustComponent = components.find(c => c.componentType === 'colorAdjustPanel');
      const fabricTypeComponent = components.find(c => c.componentType === 'typeSelector');
      // 获取款式描述组件
      const maskDescriptionComponent = components.find(c => c.componentType === 'maskDescriptionPanel');
      
      // 记录找到的组件
      if (clothingComponent) console.log('获取到服装组件:', clothingComponent);
      if (colorComponent) console.log('获取到颜色组件:', colorComponent);
      if (colorAdjustComponent) console.log('获取到颜色调整组件:', colorAdjustComponent);
      if (fabricTypeComponent) console.log('获取到面料类型组件:', fabricTypeComponent);
      if (maskDescriptionComponent) console.log('获取到款式描述组件:', maskDescriptionComponent);
      
      // 准备适配后的组件数据 - 使用数组结构
      const adaptedComponents = [];
      
      // 添加服装组件
      if (clothingComponent) {
        // 处理fileInfo，确保size是数字类型
        let fileInfo = clothingComponent.fileInfo;
        if (fileInfo) {
          // 如果size是字符串类型，尝试转换为数字
          if (typeof fileInfo.size === 'string') {
            // 尝试从字符串中提取数字
            const sizeMatch = fileInfo.size.match(/(\d+(\.\d+)?)/);
            if (sizeMatch) {
              const numericPart = parseFloat(sizeMatch[0]);
              // 根据单位转换为字节数
              if (fileInfo.size.includes('MB')) {
                fileInfo = {...fileInfo, size: Math.round(numericPart * 1024 * 1024)};
              } else if (fileInfo.size.includes('KB')) {
                fileInfo = {...fileInfo, size: Math.round(numericPart * 1024)};
              } else {
                fileInfo = {...fileInfo, size: Math.round(numericPart)};
              }
            } else {
              // 如果无法解析，使用默认值
              fileInfo = {...fileInfo, size: 2300000};
            }
          }
        }
        
        adaptedComponents.push({
          ...clothingComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'clothingPanel',
          fileInfo: fileInfo || clothingComponent.fileInfo
        });
      }
      
      // 添加颜色组件
      if (colorComponent) {
        adaptedComponents.push({
          ...colorComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'colorPanel'
        });
      }
      
      // 添加颜色调整组件
      if (colorAdjustComponent) {
        adaptedComponents.push({
          ...colorAdjustComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'colorAdjustPanel'
        });
      }
      
      // 添加面料类型组件
      if (fabricTypeComponent) {
        adaptedComponents.push({
          ...fabricTypeComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'typeSelector'
        });
      }
      
      // 添加款式描述组件（如果存在）
      if (maskDescriptionComponent) {
        adaptedComponents.push({
          ...maskDescriptionComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'maskDescriptionPanel'
        });
      }
      
      // 设置详情信息
     return {
        ...image,
        taskId: task.taskId,
        createdAt: task.createdAt,
        // 使用数组结构存储组件
        components: adaptedComponents
      };
      
    } catch (error) {
      console.error('处理图片详情时出错:', error);
      message.error('加载图片详情失败');
    }
  };

  // 处理删除服装图面板
  const handleDeleteClothingPanel = (panelId) => {
    setClothingPanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
    setHasUnsavedChanges(true);
  };

  // 处理重新上传服装图
  const handleReuploadClothing = (panel) => {
    if (panel && panel.componentId) {
      // 记录正在重新上传的面板ID
      setCurrentReuploadClothingPanelId(panel.componentId);
      // 打开上传指南
      setShowClothingUploadGuide(true);
    }
  };

  // 处理服装图状态变化
  const handleClothingStatusChange = (panelId, newStatus) => {
    setClothingPanels(prevPanels =>
      prevPanels.map(panel =>
        panel.componentId === panelId ? { ...panel, status: newStatus } : panel
      )
    );
  };

  // 修改 handleGenerate 函数
  const handleGenerate = async () => {
    console.log('开始生成...');
    
    try {
      // 检查是否有服装图面板
      if (clothingPanels.length === 0) {
        message.error('请先上传服装图片');
        return;
      }

      // 检查是否已选择颜色
      if (!selectedColor) {
        message.error('请选择目标颜色');
        return;
      }
      
      // 检查是否已选择面料类型
      if (!fabricType) {
        message.error('请选择面料类型');
        return;
      }
      
      // 验证蒙版描述是否已填写
      if ((!clothingMaskPanel.selectedTag || clothingMaskPanel.selectedTag.trim() === '') && 
          (!clothingMaskPanel.customText || clothingMaskPanel.customText.trim() === '') &&
          (!clothingMaskPanel.description || clothingMaskPanel.description.trim() === '')) {
        message.error('请选择款式标签或填写自定义描述');
        return;
      }
      
      // 获取当前用户ID
      const currentUserId = userId || getCurrentUserId() || 'developer';
      const balance = await checkUserBalance('服装复色', 'recolor', imageQuantity);
      if(balance.code !== 200){
        message.error(balance.message);
setIsProcessing(false);
        return;
      }
      // 处理可能的本地上传图片
      let clothingImageToUse = clothingPanels[0];
      
      // 设置处理状态
      setIsProcessing(true);
      
      // 检查是否有自定义上传的服装图片（同时检查文件对象和类型）
      if (clothingPanels[0].file && clothingPanels[0].source === 'upload') {
        // 保存本地临时URL，用于创建缩略图
        const localUrl = clothingPanels[0].url;
        
        // 显示上传中提示
        message.loading('正在上传服装图片...', 0);
        const {urls,fileInfos}  = await uploadFiles([clothingPanels[0].file], 'recolor');
        message.destroy();
        clothingImageToUse={
          ...clothingPanels[0],
          url: urls[0],
          serverFileName: fileInfos[0].name,
          originalImage: urls[0],
          file: undefined, // 清除file属性，避免重复上传和存储不必要的数据
          fileInfo: {
            ...(clothingPanels[0].fileInfo || {}),
            ...fileInfos[0],
            serverFileName: fileInfos[0].name // 确保在fileInfo中也设置serverFileName
          }
        }
      }

      // 创建一个新的任务ID
      const taskId = generateId(ID_TYPES.TASK);
      
      // 获取颜色预览图的数据
      const colorPreviewElement = document.querySelector('.color-preview-box');
      let colorPreviewData = null;
      
      if (colorPreviewElement) {
        // 创建一个canvas来生成预览图
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = 88;
        canvas.height = 88;
        
        // 使用选中的颜色填充canvas
        ctx.fillStyle = selectedColor;
        ctx.fillRect(0, 0, 88, 88);
        
        // 获取base64格式的预览图数据
        colorPreviewData = canvas.toDataURL('image/jpeg');
      }
      
      // 构建任务对象 - 仅使用新的components结构
      const taskData = {
        taskId: taskId,
        userId: currentUserId,
        createdAt: new Date().toISOString(),
        status: 'processing',
        imageCount: 1, // 固定生成1张图片
        taskType: 'recolor', // 指定任务类型为服装复色
        pageType: 'recolor', // 指定页面类型
        serverFileName: clothingImageToUse.serverFileName,
        primaryImageFileName: clothingImageToUse.serverFileName,
        components: [
          // 服装组件
          {
            componentType: 'clothingPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: clothingImageToUse.title || '服装',
            status: 'completed',
            serverFileName: clothingImageToUse.serverFileName,
            originalImage: clothingImageToUse.originalImage || clothingImageToUse.url,
            url: clothingImageToUse.processedFile || clothingImageToUse.url,
            fileInfo: clothingImageToUse.fileInfo || {},
            processedFile: clothingImageToUse.processedFile,
            // 包含蒙版信息（如果有）
            hasMask: clothingImageToUse.hasMask || false,
            maskData: clothingImageToUse.maskData,
            maskPath: clothingImageToUse.maskPath,
            maskFile: clothingImageToUse.maskFile
          },
          // 颜色组件
          {
            componentType: 'colorPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '颜色选择器',
            hex: selectedColor.toUpperCase(), // 确保使用大写的颜色代码
            preview: colorPreviewData, // 添加颜色预览图数据
            adjustments: colorAdjustments, // 添加颜色调整数据
            colors: selectedColors.map(color => color.colorHex)
          },
          // 面料类型组件
          {
            componentType: 'typeSelector',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '面料类型',
            value: fabricType // 使用数字值1、2或3，对应浅色系、中性色系和深色服装
          },
          // 蒙版描述组件（如果有描述内容）
          ...(clothingMaskPanel.description ? [{
            componentType: 'maskDescriptionPanel',
            componentId: clothingMaskPanel.componentId,
            name: '款式描述',
            selectedTag: clothingMaskPanel.selectedTag,
            customText: clothingMaskPanel.customText,
            description: clothingMaskPanel.description
          }] : [])
        ],
        processInfo:{
          results:[]
        },
        // 初始状态下的空图像数组 - 使用generatedImages，固定为1张
        generatedImages: [{
          imageIndex: 0,
          status: 'processing'
        }]
      };
      
      // 使用 generationAreaRef 更新任务状态，而不是直接修改本地状态
      if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      
      // 切换到结果标签页
      setActiveTab('result');
      // 生产环境下调用后端API
      try {
        // 使用 flowTask API 创建任务，而不是 createTask
        const flowTaskData = {
          ...taskData,
          workflowName: WORKFLOW_NAME.RECOLOR, // 使用常量定义的工作流名称
          params: {
            fabricType: fabricType, // 面料类型参数
            targetColor: selectedColor.replace('#', ''), // 去掉#号的颜色值
            // 如果有蒙版描述，添加到参数中
            ...(clothingMaskPanel.description ? {
              maskDescription: clothingMaskPanel.description
            } : {})
          }
        };
        await createFlowTask(flowTaskData);
        // 调用 createFlowTask 而不是 createTask
       const {promptId,instanceId,status,url} = await executeFlow(
          WORKFLOW_NAME.RECOLOR,
          {
            "10": {
              "color":selectedColor
            },
            "14":{
              "brightness": fabricType
            },
            "12":{
              "prompt":clothingMaskPanel.description
            },
            "25":{
              "url":clothingImageToUse.url
            },
            "subInfo":{
              "type": "recolor",
              "title":"服装复色",
              "count":imageQuantity
            }
          },taskId);
          setIsProcessing(false);
          setHasUnsavedChanges(false);
        if( generationAreaRef.current){
          taskData.promptId = promptId;
          taskData.instanceId = instanceId;
          taskData.url = url;
          taskData.newTask = true;
          generationAreaRef.current.setGenerationTasks(taskData);
      }
        
      } catch (error) {
        console.error('API调用失败:', error);
        message.error('创建任务失败: ' + (error.message || '未知错误'));
        setIsProcessing(false);
        
        // 更新任务状态为失败
        taskData.status = 'failed';
        taskData.errorMessage = error.message || '未知错误';
        
        // 使用 generationAreaRef 更新任务状态
        if( generationAreaRef.current){
          generationAreaRef.current.setGenerationTasks(taskData);
        }
        
        // 调用updateTask以触发失败提示音
        updateTask(taskData);
      }
    } catch (error) {
      console.error('生成过程中出错:', error);
      message.error('生成失败: ' + (error.message || '未知错误'));
      setIsProcessing(false);
    }
    
  };



  // 处理编辑任务 - 回填数据到控制面板
  const handleEditTask = (task) => {
    if (!task) return;
    
    try {
      console.log('编辑任务:', task);
      console.log('任务组件:', task.components);
      
      // 只处理数组格式的组件，不再支持对象格式
      const components = Array.isArray(task.components) ? task.components : [];
      
      console.log('处理的组件数据:', components);
      
      // 获取服装组件 - 只匹配标准名称
      const clothingComponent = components.find(c => c.componentType === 'clothingPanel');
      if (clothingComponent) {
        console.log('获取到服装组件:', clothingComponent);
        
        // 处理fileInfo，确保size是数字类型
        let fileInfo = clothingComponent.fileInfo;
        if (fileInfo) {
          // 如果size是字符串类型，尝试转换为数字
          if (typeof fileInfo.size === 'string') {
            // 尝试从字符串中提取数字
            const sizeMatch = fileInfo.size.match(/(\d+(\.\d+)?)/);
            if (sizeMatch) {
              const numericPart = parseFloat(sizeMatch[0]);
              // 根据单位转换为字节数
              if (fileInfo.size.includes('MB')) {
                fileInfo = {...fileInfo, size: Math.round(numericPart * 1024 * 1024)};
              } else if (fileInfo.size.includes('KB')) {
                fileInfo = {...fileInfo, size: Math.round(numericPart * 1024)};
              } else {
                fileInfo = {...fileInfo, size: Math.round(numericPart)};
              }
            } else {
              // 如果无法解析，使用默认值
              fileInfo = {...fileInfo, size: 2300000};
            }
          }
        } else {
          // 使用默认fileInfo
          fileInfo = {
            size: 2300000, // 约2.3MB，转换为字节数
            width: clothingComponent.width || 1024,
            height: clothingComponent.height || 1024,
            format: 'image/jpeg'
          };
        }
        
        // 准备服装数据
        setClothingPanels([{
          componentId: generateId(ID_TYPES.COMPONENT),
          title: clothingComponent.name || '已上传服装',
          originalImage: clothingComponent.originalImage || clothingComponent.url,
          url: clothingComponent.url || clothingComponent.originalImage,
          processedFile: clothingComponent.processedFile || clothingComponent.url,
          status: 'completed',
          // 转移蒙版信息（如果有）
          hasMask: Boolean(clothingComponent.hasMask || clothingComponent.maskPath || clothingComponent.maskFile),
          maskData: clothingComponent.maskData,
          maskPath: clothingComponent.maskPath,
          maskFile: clothingComponent.maskFile,
          fileInfo: fileInfo
        }]);
      } else {
        console.warn('未找到服装组件，请检查任务数据');
      }
      
      // 设置颜色设置
      const colorComponent = components.find(c => c.componentType === 'colorPanel');
      if (colorComponent) {
        console.log('获取到颜色组件:', colorComponent);
        setSelectedColor(colorComponent.hex || '#FFFFFF');
        if (colorComponent.adjustments) {
          setColorAdjustments(colorComponent.adjustments);
        }
        if (colorComponent.preview) {
          setColorPreviewData(colorComponent.preview);
        }
      } else {
        console.warn('未找到颜色组件，使用默认值');
        setSelectedColor('#FFFFFF');
        setColorAdjustments({
          hue: 0,
          saturation: 0,
          brightness: 0,
          contrast: 0
        });
      }
      
      // 设置颜色调整设置
      const colorAdjustComponent = components.find(c => c.componentType === 'colorAdjustPanel');
      if (colorAdjustComponent && colorAdjustComponent.adjustments) {
        console.log('获取到颜色调整组件:', colorAdjustComponent);
        setColorAdjustments(colorAdjustComponent.adjustments);
      }
      
      // 设置面料类型
      const fabricTypeComponent = components.find(c => c.componentType === 'typeSelector');
      if (fabricTypeComponent) {
        console.log('获取到面料类型组件:', fabricTypeComponent);
        setFabricType(fabricTypeComponent.value || fabricTypeComponent);
      } else {
        console.warn('未找到面料类型组件，使用默认值');
      }
      
      // 回填蒙版款式描述
      const maskDescriptionComponent = components.find(c => c.componentType === 'maskDescriptionPanel');
      if (maskDescriptionComponent) {
        console.log('获取到蒙版描述组件:', maskDescriptionComponent);
        // 创建全新的状态对象，不依赖于之前的状态
        const newMaskPanel = {
          componentId: generateId(ID_TYPES.COMPONENT),
          selectedTag: maskDescriptionComponent.selectedTag || '',
          customText: maskDescriptionComponent.customText || '',
          description: maskDescriptionComponent.description || ''
        };
        setClothingMaskPanel(newMaskPanel);
        console.log('回填蒙版描述:', newMaskPanel);
      } else {
        console.warn('未找到蒙版描述组件，使用默认空值');
        // 确保设置为空值
        setClothingMaskPanel({
          componentId: generateId(ID_TYPES.COMPONENT),
          selectedTag: '',
          customText: '',
          description: ''
        });
      }
      
      // 设置图片数量为固定值1
      setImageQuantity(1);
  
      // 设置为编辑模式并显示任务模态框
      setTaskModalVisible(true);
      
      // 设置当前任务的生成图片
      if (task.generatedImages && task.generatedImages.length > 0) {
        setGenResults(task.generatedImages);
      }
      
      // 显示成功消息
      message.success('已加载任务设置');
    } catch (error) {
      console.error('处理编辑任务时出错:', error);
      message.error('加载任务设置失败');
    }
  };

  // 添加点击外部关闭下拉菜单的处理
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdowns = document.querySelectorAll('.dropdown-menu');
      dropdowns.forEach(dropdown => {
        if (!dropdown.parentElement.contains(event.target)) {
          dropdown.classList.remove('show');
        }
      });
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);


  // 添加点击外部关闭弹出层的处理函数
  useEffect(() => {
    const handleClickOutside = (event) => {
      // 检查点击是否发生在文本按钮上
      const isTextButton = event.target.closest('.text-button');
      // 检查点击是否发生在弹窗内部
      const isInsidePopup = event.target.closest('.text-popup');
      
      // 如果点击既不是文本按钮也不是弹窗内部，则关闭所有弹窗
      if (!isTextButton && !isInsidePopup) {
        setShowAdvancedText(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 计算初始缩放比例
  const calculateInitialScale = (img) => {
    if (!img) return 100;
    const container = img.parentElement;
    if (!container) return 100;
    
    // 计算图片在容器中的实际显示尺寸与真实尺寸的比例
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const imageNaturalWidth = img.naturalWidth;
    const imageNaturalHeight = img.naturalHeight;
    
    // 计算图片适应容器时的尺寸
    const containerRatio = containerWidth / containerHeight;
    const imageRatio = imageNaturalWidth / imageNaturalHeight;
    
    let scale;
    if (imageRatio > containerRatio) {
      // 图片较宽，以容器宽度为基准
      scale = (containerWidth / imageNaturalWidth) * 100;
    } else {
      // 图片较高，以容器高度为基准
      scale = (containerHeight / imageNaturalHeight) * 100;
    }

    // 如果计算出的缩放比例大于100%，说明图片实际尺寸小于容器
    // 这种情况下我们应该将图片显示为其实际大小
    if (scale > 100) {
      scale = 100;
    }
    
    // 返回相对于真实尺寸的缩放百分比，四舍五入到整数
    return Math.round(scale);
  };

  // 处理图片加载完成
  const handleImageLoad = (e) => {
    const img = e.target;
    const initialScaleValue = calculateInitialScale(img);
    setInitialScale(initialScaleValue);  // 设置初始比例
    setImageScale(initialScaleValue);    // 设置当前比例
    imageRef.current = img;
  };


  const [currentTaskId, setCurrentTaskId] = useState(null);
  const [isUsingExistingTask, setIsUsingExistingTask] = useState(false);
  const [generatedImages, setGeneratedImages] = useState([]);
  const [genResults, setGenResults] = useState([]);
  const [taskStatus, setTaskStatus] = useState('idle');

  // 处理打开蒙版绘制弹窗 - 保留空函数架构避免报错
  const handleDrawMask = (panel) => {
    // 注意：这个函数在服装复色页面中会被ImageInfoModal组件调用
    // 但服装复色功能不需要绘制蒙版，所以这里禁用了该功能
    // 在其他页面如模特换装(try-on)中，这个函数会打开蒙版绘制弹窗
    // 我们通过ImageInfoModal组件中的条件判断来控制按钮是否显示，
    // 但为了接口统一，仍然保留这个函数，只是不执行实际操作
    console.log('蒙版功能已禁用');
    message.info('服装复色功能不支持蒙版绘制');
  };


  // 修改 handleColorSelect 函数
  const handleColorSelect = (color) => {
    // 如果color为null，表示清空颜色选择
    // 只有当颜色真的变化时才设置
    if (color !== selectedColor) {
      setSelectedColor(color); // 设置新选中的颜色
      // 重置颜色微调组件的值
      setColorAdjustments(DEFAULT_VALUES);
      
      // 保存到历史记录
      if (color) {
        saveColorToHistory(color);
      }
      
      // 设置未保存更改状态
      setHasUnsavedChanges(true);
    }
    setShowColorPicker(false);
  };

  // 修改 handleSaveColorAdjustment 函数
  const handleSaveColorAdjustment = (adjustments) => {
    if (!selectedColor) return;
    
    // 获取选中颜色的RGB值
    const hex = selectedColor.replace('#', '').toUpperCase();
    let r = parseInt(hex.substr(0, 2), 16);
    let g = parseInt(hex.substr(2, 2), 16);
    let b = parseInt(hex.substr(4, 2), 16);
    
    // 转换为HSL
    const hsl = toHsl(r, g, b);
    
    // 应用调整：色调、饱和度、亮度
    const newHue = (hsl.h + adjustments.hue) % 360;
    const newSaturation = Math.max(0, Math.min(100, hsl.s * (1 + adjustments.saturation)));
    const newLightness = Math.max(0, Math.min(100, hsl.l * (1 + adjustments.brightness)));
    
    // 转回RGB
    const newRgb = fromHsl(newHue, newSaturation, newLightness);
    
    // 应用对比度
    const contrastAdjustedRgb = applyContrast(newRgb, adjustments.contrast);
    
    // 转为16进制（确保使用大写）
    const adjustedColor = toHex(
      contrastAdjustedRgb.r, 
      contrastAdjustedRgb.g, 
      contrastAdjustedRgb.b
    ).toUpperCase();
    
    // 更新选中的颜色
    handleColorSelect(adjustedColor);
    
    // 重置调整值（因为新颜色已经包含了调整）
    setColorAdjustments(DEFAULT_VALUES);
  };

  // 处理面料类型选择
  const handleFabricTypeSelect = (type) => {
    setFabricType(type); // 直接保存数字选项值（1、2或3）
    console.log('已选择面料类型:', type); // 记录数字选项值
    
    // 设置未保存更改状态
    setHasUnsavedChanges(true);
    
    // TODO: 后续集成 comfyUI 工作流处理
    // 当前选项值 type (1, 2, 3) 将被传递给 comfyUI 工作流的对应节点
    // 1 = 浅色服装
    // 2 = 中性色服装
    // 3 = 深色服装
  };

  // 处理颜色面板点击
  const handleColorPanelClick = (panel, position) => {
    setColorPickerPosition(position);
    setShowColorPicker(true);
  };

  // 处理颜色调整变化
  const handleColorAdjustmentChange = (adjustments) => {
    // 保存调整值
    setColorAdjustments(adjustments);
    
    // 设置未保存更改状态
    setHasUnsavedChanges(true);
    
    // 如果有选择的颜色，根据调整计算新的预览颜色
    if (selectedColor) {
      // 获取选中颜色的RGB值
      const hex = selectedColor.replace('#', '').toUpperCase();
      let r = parseInt(hex.substr(0, 2), 16);
      let g = parseInt(hex.substr(2, 2), 16);
      let b = parseInt(hex.substr(4, 2), 16);
      
      // 转换为HSL
      const hsl = toHsl(r, g, b);
      
      // 应用调整：色调、饱和度、亮度
      const newHue = (hsl.h + adjustments.hue) % 360;
      const newSaturation = Math.max(0, Math.min(100, hsl.s * (1 + adjustments.saturation)));
      const newLightness = Math.max(0, Math.min(100, hsl.l * (1 + adjustments.brightness)));
      
      // 转回RGB
      const newRgb = fromHsl(newHue, newSaturation, newLightness);
      
      // 应用对比度调整
      const contrastAdjustedRgb = applyContrast(newRgb, adjustments.contrast);
      
      // 转为16进制（确保使用大写）
      const adjustedColor = toHex(
        contrastAdjustedRgb.r, 
        contrastAdjustedRgb.g, 
        contrastAdjustedRgb.b
      ).toUpperCase();
      
      // 更新颜色预览（但不改变基础选中色）
      const colorPreview = document.querySelector('.color-preview-box');
      if (colorPreview) {
        colorPreview.style.backgroundColor = adjustedColor;
      }
    }
  };
  
  // 添加组件卸载时释放临时URL的钩子
  useEffect(() => {
    // 返回清理函数
    return () => {
      // 在组件卸载时释放所有临时URL
      if (clothingPanels && clothingPanels.length > 0) {
        clothingPanels.forEach(panel => {
          if (panel.url && typeof panel.url === 'string' && panel.url.startsWith('blob:')) {
            console.log('释放临时URL:', panel.url);
            URL.revokeObjectURL(panel.url);
          }
        });
      }
    };
  }, [clothingPanels]);


  const [clothingMaskPanel, setClothingMaskPanel] = useState({
    componentId: generateId(ID_TYPES.COMPONENT),
    selectedTag: '',
    customText: '',
    description: ''
  });

  // 处理蒙版描述面板变化
  const handleMaskPanelChange = (panel) => {
    if (panel.name === '款式描述' || panel.componentId === clothingMaskPanel.componentId) {
      setClothingMaskPanel(panel);
    }
  };

  // 添加保存颜色历史记录的函数，确保与原函数功能一致但命名不同
  const saveColorToHistory = (color) => {
    try {
      // 确保color是字符串类型
      if (!color || typeof color !== 'string') {
        console.error('无效的颜色值或颜色不是字符串类型:', color);
        return;
      }
      
      // 获取现有的历史记录
      const storedHistory = localStorage.getItem('colorHistory');
      let history = storedHistory ? JSON.parse(storedHistory) : [];
      
      // 确保颜色是大写格式
      color = color.toUpperCase();
      
      // 如果颜色已存在，先移除它
      history = history.filter(c => c !== color);
      
      // 将新颜色添加到开头
      history.unshift(color);
      
      // 限制历史记录数量为10个
      if (history.length > 10) {
        history = history.slice(0, 10);
      }
      
      // 保存回本地存储
      localStorage.setItem('colorHistory', JSON.stringify(history));
    } catch (error) {
      console.error('保存颜色到历史记录时出错:', error);
    }
  };

  return (
    <RequireLogin isLoggedIn={isLoggedIn} featureName="服装复色功能">
      <PromptIfUnsaved when={hasUnsavedChanges} message="操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）" />
      <div className="recolor-page">
        <div className="recolor-container" ref={containerRef}>
          <ControlPanel
            ref={controlPanelRef}
            width={`${controlPanelWidth}%`}
            onGenerate={handleGenerate}
            disabled={isProcessing}
            featureName="recolor"
            quantity={imageQuantity}
          >
            {/* 服装图上传区域或服装图面板 - 位于最上方 */}
            {clothingPanels.length === 0 ? (
              <UploadBox
                id="clothing-upload-box"
                onUpload={handleClothingFileUpload}
                onShowGuide={() => {
                  setUploadGuideType('recolorClothing');
                  setShowClothingUploadGuide(true);
                }}
                onUploadResult={handleClothingUploadResult}
                panels={clothingPanels}
                className="mt-2"
                showSupportTag={false}
                pageType="recolor"
                uploadType="clothing"
              />
            ) : (
              // 展示服装图面板
              <div className="clothing-cards">
                {clothingPanels.map(panel => (
                  <ClothingPanel 
                    key={panel.componentId}
                    panel={panel}
                    onClick={() => {
                      console.log('点击服装卡片:', panel);
                    }}
                    onDeleteClick={() => handleDeleteClothingPanel(panel.componentId)}
                    onReuploadClick={() => handleReuploadClothing(panel)}
                    onDrawMask={() => handleDrawMask(panel)}
                    onStatusChange={(newStatus) => handleClothingStatusChange(panel.componentId, newStatus)}
                    showFileInfo={true}
                    onPanelsChange={setClothingPanels}
                    pageType="recolor"
                    onExpandClick={(panel, position) => {
                      setOperationsPanel({
                        panel: panel,
                        position
                      });
                    }}
                  />
                ))}
              </div>
            )}
            
            {/* 服装蒙版描述面板 - 位于服装面板下方 */}
            {clothingPanels.length > 0 && (
              <MaskDescriptionPanel
                panel={clothingMaskPanel}
                onPanelsChange={handleMaskPanelChange}
              />
            )}
            
            {/* 类型选择组件 - 位于蒙版描述面板下方 */}
            <TypeSelector
              onSelect={handleFabricTypeSelect}
              defaultValue={fabricType}
              pageType="recolor"
            />

            {/* 颜色选择面板 */}
            <ColorPanel
              selectedColor={selectedColor}
              onExpandClick={(panel, position) => handleColorPanelClick(panel, position)}
              useCustomIcon={true}
            />

            {/* 颜色微调面板 */}
            {selectedColor && (
              <ColorAdjustPanel
                initialValues={colorAdjustments}
                onChange={handleColorAdjustmentChange}
                selectedColor={selectedColor}
                onSave={handleSaveColorAdjustment}
              />
            )}
            
            {/* 提示组件 */}
            <TipsPanel 
              tipContent={
                <>
                  此功能不适合多色的面料（如印花、撞色拼接等），尽量使用纯色的面料，否则可能需要PS进行后期润色。
                </>
              }
            />
          </ControlPanel>
          
          {/* 颜色选择器弹窗 */}
          {showColorPicker && (
            <ColorPickerModal
              isOpen={true}
              onClose={() => setShowColorPicker(false)}
              initialColor={selectedColor || '#FF3C6A'}
              onColorSelect={handleColorSelect}
              style={{
                top: `${colorPickerPosition.top - 128}px`,
                left: `${colorPickerPosition.left}px`,
                zIndex: 1001,
                maxWidth: 'calc(100vw - 40px)',
                overflow: 'auto'
              }}
            />
          )}

          <ResizeHandle
            ref={handleRef}
            containerRef={containerRef}
            onResize={setControlPanelWidth}
            minWidth={25}
            maxWidth={50}
          />

                  <GenerationArea
            ref={generationAreaRef}    setIsProcessing={setIsProcessing}

            activeTab={activeTab}
            onTabChange={setActiveTab}
            onEditTask={handleEditTask}
            onViewDetails={handleViewDetails}
            pageType="recolor"
          />
        </div>

        {/* 上传指南模态框 */}
        {showUploadGuide && (
          <UploadGuideModal
            type={uploadGuideType}
            pageType="recolor"
            onClose={() => setShowUploadGuide(false)}
            onUpload={(result) => {
              console.log('收到上传结果:', result);
              handleUploadResult(result);
              
              // 根据结果中的shouldClose字段决定是否关闭弹窗
              if (result.shouldClose !== false) {
                setShowUploadGuide(false);
              }
            }}
          />
        )}

        {/* 服装图上传指南弹窗 */}
        {showClothingUploadGuide && (
          <UploadGuideModal
            type="recolorClothing"
            pageType="recolor"
            onClose={() => setShowClothingUploadGuide(false)}
            onUpload={(result) => {
              console.log('收到服装图上传结果:', result);
              handleClothingUploadResult(result);
              
              // 如果不是会话控制的关闭（例如取消按钮或点击外部），则主动关闭弹窗
              if (result.shouldClose) {
                setShowClothingUploadGuide(false);
              }
            }}
          />
        )}

        {/* 添加操作弹窗 */}
        {operationsPanel && (
          <ImageInfoModal
            panel={operationsPanel.panel}
            position={operationsPanel.position}
            onClose={() => setOperationsPanel(null)}
            onDelete={handleDeleteClothingPanel}
            onReupload={handleReuploadClothing}
            onDrawMask={handleDrawMask}
            pageType="recolor"
          />
        )}



        {/* 显示上传的服装图片和生成按钮 */}
        {currentTaskId && !isProcessing && (
          <div className="action-buttons">
            <Button 
              type="primary" 
              onClick={handleGenerate}
              disabled={isProcessing}
            >
              开始生成
            </Button>
          </div>
        )}
        
        {/* 显示生成中的加载状态 */}
        {isProcessing && (
          <div className="generating-status">
            <Spin size="large" />
            <p>正在生成中，请稍候...</p>
          </div>
        )}
        
        {/* 显示生成的图片 */}
        {processedImages.length > 0 && (
          <div className="generated-images">
            <h3>生成结果</h3>
            <div className="image-grid">
              {processedImages.map((image, index) => (
                <div key={index} className="image-item">
                  <img src={image.url} alt={`生成图片 ${index + 1}`} />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </RequireLogin>
  );
};

export default RecolorPage; 