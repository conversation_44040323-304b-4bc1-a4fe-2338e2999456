/**
 * 统一工作流执行服务
 * 整合ComfyUI和RunningHub平台，对前端透明
 */

const Config = require('config');
const { createError } = require('../../utils/error');
const WorkflowService = require('../comfyClient/service/WorkflowService');
const RunningHubService = require('../runningHub/runningHubService');
const FlowTask = require('../../models/FlowTask');
const RunningHubConfig = require('../../models/RunningHubConfig');
const {
  getPlatformConfig,
  getRecommendedPlatform,
  isWorkflowSupportedOnPlatform,
  convertParameters,
  getComfyUIInstancesStatus
} = require('../../config/platformConfig');

class UnifiedWorkflowService {
  constructor() {
    this.comfyWorkflowService = null;
    this.runningHubService = new RunningHubService();
    this.platformConfig = getPlatformConfig();
  }

  /**
   * 初始化ComfyUI工作流服务
   * @param {WorkflowService} workflowService - ComfyUI工作流服务实例
   */
  setComfyWorkflowService(workflowService) {
    this.comfyWorkflowService = workflowService;
  }

  /**
   * 智能选择执行平台
   * @param {string} workflowName - 工作流名称
   * @param {Object} params - 执行参数
   * @param {Object} context - 上下文信息（用户信息等）
   * @returns {Promise<string>} 选择的平台 ('comfyui' | 'runninghub')
   */
  async selectPlatform(workflowName, params, context = {}) {
    // 1. 检查是否有强制指定平台
    if (params.forcePlatform) {
      if (isWorkflowSupportedOnPlatform(workflowName, params.forcePlatform)) {
        return params.forcePlatform;
      } else {
        console.warn(`强制指定的平台 ${params.forcePlatform} 不支持工作流 ${workflowName}，将自动选择`);
      }
    }

    // 2. 确保上下文中有用户ID
    if (!context.userId && context.user && context.user.id) {
      context.userId = context.user.id;
    }

    // 3. 使用新的优先级策略选择平台
    const platformSelection = await this.selectPlatformWithPriority(workflowName, context);

    console.log(`工作流 ${workflowName} 选择平台: ${platformSelection.platform}, 原因: ${platformSelection.reason}`);

    // 如果选择了RunningHub且有配置信息，保存到上下文中
    if (platformSelection.platform === 'runninghub' && platformSelection.config) {
      context.selectedConfig = platformSelection.config;
      context.selectedWorkflowId = platformSelection.workflowId;
    }

    // 保存平台选择信息到上下文
    context.platformSelection = platformSelection;

    return platformSelection.platform;
  }

  /**
   * 基于优先级策略选择平台
   * 优先级：ComfyUI可用实例 > ComfyUI开机 > RunningHub可用配置 > ComfyUI繁忙实例
   * @param {string} workflowName - 工作流名称
   * @param {Object} context - 上下文信息
   * @returns {Promise<Object>} 平台选择结果
   */
  async selectPlatformWithPriority(workflowName, context = {}) {
    const instanceService = require('../instance/instanceService');

    try {
      // 1. 获取ComfyUI实例状态
      const comfyuiInstancesStatus = await getComfyUIInstancesStatus();
      context.comfyuiInstancesStatus = comfyuiInstancesStatus;

      console.log(`ComfyUI实例状态: 总计${comfyuiInstancesStatus.totalInstances}个 (运行中${comfyuiInstancesStatus.totalRunningInstances}个，关机${comfyuiInstancesStatus.totalShutdownInstances}个)，空闲${comfyuiInstancesStatus.availableRunningInstances}个，繁忙${comfyuiInstancesStatus.busyRunningInstances}个，总可用资源${comfyuiInstancesStatus.totalAvailableResources}个`);

      // 2. 优先级1：如果有空闲的运行中ComfyUI实例，直接使用
      if (comfyuiInstancesStatus.availableRunningInstances > 0) {
        return {
          platform: 'comfyui',
          reason: `ComfyUI有空闲实例 (${comfyuiInstancesStatus.availableRunningInstances}个空闲/${comfyuiInstancesStatus.totalRunningInstances}个运行中)`,
          config: null
        };
      }

      // 3. 优先级2：如果有关机的ComfyUI实例，尝试启动
      if (comfyuiInstancesStatus.totalShutdownInstances > 0) {
        console.log(`尝试启动关机的ComfyUI实例 (${comfyuiInstancesStatus.totalShutdownInstances}个关机实例可用)`);
        const startupResult = await this.tryStartComfyUIInstance(instanceService);
        if (startupResult.success) {
          return {
            platform: 'comfyui',
            reason: `成功启动ComfyUI实例: ${startupResult.instanceId} (从${comfyuiInstancesStatus.totalShutdownInstances}个关机实例中选择)`,
            config: null,
            startedInstance: startupResult.instanceId
          };
        } else {
          console.log(`启动ComfyUI实例失败: ${startupResult.reason}，将尝试RunningHub平台`);
        }
      }

      // 4. 优先级3：检查RunningHub配置是否可用
      const runningHubSelection = await this.selectAvailableRunningHubConfig(workflowName, context.userId);
      if (runningHubSelection.available) {
        return {
          platform: 'runninghub',
          reason: runningHubSelection.reason,
          config: runningHubSelection.config,
          workflowId: runningHubSelection.workflowId,
          configsUsage: runningHubSelection.configsUsage
        };
      }

      // 5. 优先级4：如果RunningHub配置都满了，回到ComfyUI实例
      // 注意：关机的实例也可以通过手动开机来使用，所以也算作可用资源
      if (comfyuiInstancesStatus.totalAvailableResources > 0) {
        const resourceDescription = [];
        if (comfyuiInstancesStatus.busyRunningInstances > 0) {
          resourceDescription.push(`${comfyuiInstancesStatus.busyRunningInstances}个繁忙实例(排队等待)`);
        }
        if (comfyuiInstancesStatus.totalShutdownInstances > 0) {
          resourceDescription.push(`${comfyuiInstancesStatus.totalShutdownInstances}个关机实例(可手动开机)`);
        }

        return {
          platform: 'comfyui',
          reason: `所有RunningHub配置已达并发限制，使用ComfyUI可用资源 (${resourceDescription.join('，')})`,
          config: null,
          fallbackToBusy: true
        };
      }

      // 6. 最后的后备方案：强制使用RunningHub（忽略并发限制）
      const forceRunningHubSelection = await this.selectAvailableRunningHubConfig(workflowName, context.userId, true);
      if (forceRunningHubSelection.available) {
        return {
          platform: 'runninghub',
          reason: '强制使用RunningHub配置（忽略并发限制）',
          config: forceRunningHubSelection.config,
          workflowId: forceRunningHubSelection.workflowId,
          forcedSelection: true
        };
      }

      // 7. 如果都不可用，抛出错误
      throw new Error('没有可用的执行平台');

    } catch (error) {
      console.error('平台选择失败:', error);
      // 默认回退到ComfyUI
      return {
        platform: 'comfyui',
        reason: `平台选择出错，回退到ComfyUI: ${error.message}`,
        config: null,
        error: error.message
      };
    }
  }

  /**
   * 尝试启动关机的ComfyUI实例
   * @param {Object} instanceService - 实例服务
   * @returns {Promise<Object>} 启动结果
   */
  async tryStartComfyUIInstance(instanceService) {
    try {
      console.log('尝试启动关机的ComfyUI实例...');

      // 使用现有的实例API获取实例列表
      const ResourceAPI = require('../../config/ResourceAPI');
      const instanceAxios = require('../../utils/instanceAxios');

      const response = await instanceAxios.get(ResourceAPI.INSTANCE_LIST.url, {
        params: {
          page: 1,
          limit: 200,
        }
      });

      if (response.data.code !== 0) {
        return { success: false, reason: '获取实例列表失败' };
      }

      const instances = response.data.data.appList || [];
      if (instances.length === 0) {
        return { success: false, reason: '没有可用的实例' };
      }

      // 查找关机的实例（状态800表示关机）
      const shutdownInstances = instances.filter(instance =>
        instance.status === 800
      );

      if (shutdownInstances.length === 0) {
        return { success: false, reason: '没有关机的实例可以启动' };
      }

      // 随机选择一个关机的实例进行启动
      const randomIndex = Math.floor(Math.random() * shutdownInstances.length);
      const instanceToStart = shutdownInstances[randomIndex];

      console.log(`尝试启动实例: ${instanceToStart.appId} (${instanceToStart.customName})`);

      try {
        // 启动实例
        await instanceService.startInstance(instanceToStart.appId);

        // 等待实例就绪（最多等待2分钟）
        await instanceService.waitForInstanceReady(instanceToStart.appId, 120000);

        console.log(`实例 ${instanceToStart.appId} 启动成功`);
        return {
          success: true,
          instanceId: instanceToStart.appId,
          instanceName: instanceToStart.customName
        };
      } catch (startError) {
        console.error(`启动实例 ${instanceToStart.appId} 失败:`, startError);
        return {
          success: false,
          reason: `启动实例失败: ${startError.message}`,
          instanceId: instanceToStart.appId
        };
      }
    } catch (error) {
      console.error('尝试启动ComfyUI实例时出错:', error);
      return { success: false, reason: `启动过程出错: ${error.message}` };
    }
  }

  /**
   * 选择可用的RunningHub配置
   * @param {string} workflowName - 工作流名称
   * @param {string} userId - 用户ID
   * @param {boolean} ignoreLimit - 是否忽略并发限制
   * @returns {Promise<Object>} 配置选择结果
   */
  async selectAvailableRunningHubConfig(workflowName, userId = null, ignoreLimit = false) {
    try {
      // 获取所有启用的配置，按使用次数升序排列
      let configs;
      if (userId) {
        // 优先获取用户的配置
        configs = await RunningHubConfig.find({
          createdBy: userId,
          enabled: true
        }).sort({ 'usage.totalTasks': 1 });

        // 如果用户没有配置，获取所有启用的配置作为后备
        if (!configs || configs.length === 0) {
          configs = await RunningHubConfig.find({
            enabled: true
          }).sort({ 'usage.totalTasks': 1 });
        }
      } else {
        configs = await RunningHubConfig.find({
          enabled: true
        }).sort({ 'usage.totalTasks': 1 });
      }

      if (!configs || configs.length === 0) {
        return {
          available: false,
          reason: '没有可用的RunningHub配置',
          config: null
        };
      }

      // 筛选支持该工作流的配置
      const availableConfigs = configs.filter(config =>
        config.workflowMappings.has(workflowName)
      );

      if (availableConfigs.length === 0) {
        return {
          available: false,
          reason: `没有配置支持工作流 ${workflowName}`,
          config: null
        };
      }

      // 如果不忽略限制，检查并发限制
      if (!ignoreLimit) {
        // 检查每个配置的当前运行任务数
        const configsWithConcurrency = await Promise.all(
          availableConfigs.map(async (config) => {
            const runningTasks = await FlowTask.countDocuments({
              runningHubConfig: config._id,
              status: { $in: ['pending', 'processing'] }
            });

            return {
              ...config.toObject(),
              currentRunningTasks: runningTasks,
              canAcceptNewTask: runningTasks < 10 // 每个配置最多10个并发任务
            };
          })
        );

        // 查找可以接受新任务的配置
        const availableConfig = configsWithConcurrency.find(config => config.canAcceptNewTask);

        if (!availableConfig) {
          return {
            available: false,
            reason: '所有RunningHub配置都已达到并发限制(10个任务)',
            config: null,
            configsUsage: configsWithConcurrency.map(config => ({
              name: config.name,
              currentRunningTasks: config.currentRunningTasks,
              totalTasks: config.usage.totalTasks || 0
            }))
          };
        }

        // 返回可用的配置
        const selectedConfig = availableConfigs.find(c => c._id.toString() === availableConfig._id.toString());
        return {
          available: true,
          reason: `选择配置: ${selectedConfig.name} (当前运行: ${availableConfig.currentRunningTasks}/10)`,
          config: selectedConfig,
          workflowId: selectedConfig.workflowMappings.get(workflowName),
          configsUsage: configsWithConcurrency.map(config => ({
            name: config.name,
            currentRunningTasks: config.currentRunningTasks,
            totalTasks: config.usage.totalTasks || 0,
            selected: config._id.toString() === selectedConfig._id.toString()
          }))
        };
      } else {
        // 忽略限制，选择使用次数最少的配置
        const selectedConfig = availableConfigs[0];
        return {
          available: true,
          reason: `强制选择配置: ${selectedConfig.name} (忽略并发限制)`,
          config: selectedConfig,
          workflowId: selectedConfig.workflowMappings.get(workflowName)
        };
      }

    } catch (error) {
      console.error('选择RunningHub配置失败:', error);
      return {
        available: false,
        reason: `选择配置时出错: ${error.message}`,
        config: null,
        error: error.message
      };
    }
  }

  /**
   * 统一执行工作流
   * @param {string} workflowName - 工作流名称
   * @param {Object} params - 执行参数
   * @param {string} userId - 用户ID
   * @param {string} taskId - 任务ID
   * @param {Function} callback - 回调函数
   * @param {Object} userContext - 用户上下文信息
   * @returns {Promise<Object>} 执行结果
   */
  async executeWorkflow(workflowName, params, userId, taskId, callback, userContext = {}) {
    try {
      // 构建上下文信息
      const context = {
        user: {
          id: userId,
          isVip: userContext.isVip || false,
          isPaid: userContext.isPaid || false,
          ...userContext
        }
      };

      // 选择执行平台并获取详细推荐信息
      const platform = await this.selectPlatform(workflowName, params, context);

      // 使用新的平台选择信息（已保存在context.platformSelection中）
      const platformSelection = context.platformSelection || {};

      console.log(`任务 ${taskId} 选择平台: ${platform}，用户: ${userId}，原因: ${platformSelection.reason}`);

      // 更新任务记录平台信息
      const platformInfo = {
        selectedAt: new Date(),
        reason: platformSelection.reason || '未知原因',
        comfyuiInstancesStatus: context.comfyuiInstancesStatus,
        selectionMethod: 'priority_based' // 标记使用新的优先级选择方法
      };

      // 如果启动了ComfyUI实例，记录启动信息
      if (platformSelection.startedInstance) {
        platformInfo.startedInstance = platformSelection.startedInstance;
      }

      // 如果是回退到繁忙实例，记录标记
      if (platformSelection.fallbackToBusy) {
        platformInfo.fallbackToBusy = true;
      }

      // 如果是强制选择RunningHub，记录标记
      if (platformSelection.forcedSelection) {
        platformInfo.forcedSelection = true;
      }

      // 如果选择了RunningHub配置，记录配置信息
      if (platformSelection.config) {
        platformInfo.runningHubConfig = {
          configId: platformSelection.config._id,
          configName: platformSelection.config.name,
          workflowId: platformSelection.workflowId,
          usageCount: platformSelection.config.usage.totalTasks || 0
        };
      }

      // 如果有配置使用统计，记录
      if (platformSelection.configsUsage) {
        platformInfo.configsUsage = platformSelection.configsUsage;
      }

      await FlowTask.findOneAndUpdate(
        { taskId },
        {
          platform,
          platformInfo
        },
        { new: true }
      );

      // 根据平台执行
      if (platform === 'runninghub') {
        return await this.executeOnRunningHub(workflowName, params, userId, taskId, callback, context);
      } else {
        return await this.executeOnComfyUI(workflowName, params, userId, taskId, callback, userContext);
      }

    } catch (error) {
      console.error('统一工作流执行失败:', error);
      throw error;
    }
  }

  /**
   * 在RunningHub平台执行工作流
   * @param {string} workflowName - 工作流名称
   * @param {Object} params - 执行参数
   * @param {string} userId - 用户ID
   * @param {string} taskId - 任务ID
   * @param {Function} callback - 回调函数
   * @param {Object} context - 上下文信息（包含选择的配置）
   * @returns {Promise<Object>} 执行结果
   */
  async executeOnRunningHub(workflowName, params, userId, taskId, callback, context = {}) {
    let config;
    let runningHubWorkflowId;

    try {

      // 优先使用智能选择的配置
      if (context.selectedConfig && context.selectedWorkflowId) {
        config = context.selectedConfig;
        runningHubWorkflowId = context.selectedWorkflowId;
        console.log(`使用智能选择的配置: ${config.name} (使用次数: ${config.usage.totalTasks || 0})`);
      } else {
        // 后备方案：从数据库获取用户的默认RunningHub配置
        config = await RunningHubConfig.getDefaultConfig(userId);
        if (!config) {
          throw createError(400, '未找到可用的RunningHub配置，请先在管理页面添加配置');
        }

        // 获取工作流ID映射
        runningHubWorkflowId = config.getWorkflowId(workflowName);
        if (!runningHubWorkflowId) {
          throw createError(400, `工作流 ${workflowName} 在当前RunningHub配置中没有对应的映射`);
        }
        console.log(`使用默认配置: ${config.name}`);
      }

      if (!config.enabled) {
        throw createError(400, '当前RunningHub配置已禁用');
      }

      // 创建RunningHub服务实例并设置API密钥
      const runningHubService = new RunningHubService();
      runningHubService.setApiKey(config.getFullApiKey());

      // 转换参数格式
      const runningHubParams = this.convertParamsForRunningHub(params, workflowName);

      // 创建RunningHub任务
      const createResult = await runningHubService.createAdvancedTask({
        apiKey: config.getFullApiKey(),
        workflowId: runningHubWorkflowId,
        addMetadata: false,
        ...runningHubParams
      });

      if (!createResult.success) {
        throw createError(500, `RunningHub任务创建失败: ${createResult.error}`);
      }

      const runningHubTaskId = createResult.data.taskId;

      // 更新配置使用统计
      await config.updateUsage(true);

      // 调用回调函数更新任务状态
      if (callback) {
        await callback(runningHubTaskId, 'runninghub', 'https://www.runninghub.cn');
      }

      // 更新任务记录，保存配置信息
      await FlowTask.findOneAndUpdate(
        { taskId },
        {
          runningHubConfig: config._id,
          runningHubTaskId: runningHubTaskId,
          runningHubWorkflowId: runningHubWorkflowId
        },
        { new: true }
      );

      return {
        success: true,
        platform: 'runninghub',
        promptId: runningHubTaskId,
        instanceId: '',
        url: createResult.netWssUrl,
        taskId: runningHubTaskId,
        originalTaskId: taskId,
        configId: config._id,
        // RunningHub特有信息
        netWssUrl: createResult.netWssUrl,
        clientId: createResult.clientId,
        taskStatus: createResult.taskStatus,
        promptTips: createResult.promptTips,
        promptTipsData: createResult.promptTipsData,
        // 传递服务实例用于后续处理
        runningHubService: runningHubService,
        client: runningHubService // 兼容现有异步处理逻辑
      };

    } catch (error) {
      console.error('RunningHub执行失败:', error);

      // 如果有配置，更新失败统计
      try {
        if (config) {
          await config.updateUsage(false);
        }
      } catch (updateError) {
        console.error('更新RunningHub配置使用统计失败:', updateError);
      }

      throw error;
    }
  }

  /**
   * 在ComfyUI平台执行工作流
   * @param {string} workflowName - 工作流名称
   * @param {Object} params - 执行参数
   * @param {string} userId - 用户ID
   * @param {string} taskId - 任务ID
   * @param {Function} callback - 回调函数
   * @param {Object} userContext - 用户上下文信息（包含优先实例ID）
   * @returns {Promise<Object>} 执行结果
   */
  async executeOnComfyUI(workflowName, params, userId, taskId, callback, userContext = {}) {
    try {
      if (!this.comfyWorkflowService) {
        throw createError(500, 'ComfyUI工作流服务未初始化');
      }

      // 使用原有的ComfyUI执行逻辑，传递用户上下文以支持实例连续性
      const result = await this.comfyWorkflowService.executeWorkflow(
        workflowName,
        params,
        userId,
        taskId,
        callback,
        userContext
      );

      return {
        ...result,
        platform: 'comfyui'
      };

    } catch (error) {
      console.error('ComfyUI执行失败:', error);
      throw error;
    }
  }

  /**
   * 转换参数格式为RunningHub格式
   * @param {Object} params - 原始参数
   * @param {string} workflowName - 工作流名称
   * @returns {Object} 转换后的参数
   */
  convertParamsForRunningHub(params, workflowName) {
    // 使用统一的参数转换系统
    return convertParameters(params, 'comfyui', 'runninghub', workflowName);
  }

  /**
   * 统一处理工作流结果
   * @param {string} platform - 执行平台
   * @param {string} promptId - 提示ID或任务ID
   * @param {string} instanceId - 实例ID
   * @param {string} workflowName - 工作流名称
   * @param {string} taskId - 任务ID
   * @param {string} url - 平台URL
   * @param {Object} client - 客户端实例
   * @param {Object} executeResult - 执行结果
   * @returns {Promise<Object>} 处理结果
   */
  async processWorkflowResult(platform, promptId, instanceId, workflowName, taskId, url, client, executeResult) {
    try {
      if (platform === 'runninghub') {
        return await this.processRunningHubResult(promptId, workflowName, taskId, executeResult);
      } else {
        return await this.processComfyUIResult(promptId, instanceId, workflowName, client, executeResult);
      }
    } catch (error) {
      console.error('处理工作流结果失败:', error);
      throw error;
    }
  }

  /**
   * 处理RunningHub结果
   * @param {string} runningHubTaskId - RunningHub任务ID
   * @param {string} workflowName - 工作流名称
   * @param {string} taskId - 本地任务ID
   * @param {Object} executeResult - 执行结果，包含runningHubService实例
   * @returns {Promise<Object>} 处理结果
   */
  async processRunningHubResult(runningHubTaskId, workflowName, taskId, executeResult) {
    try {
      console.log(`开始处理RunningHub任务结果: ${runningHubTaskId}`);

      // 从executeResult中获取runningHubService实例
      const runningHubService = executeResult.runningHubService;
      if (!runningHubService) {
        throw new Error('RunningHub服务实例不可用');
      }

      // 检查executeResult中是否已有初始状态信息
      let currentStatus = executeResult.taskStatus || 'UNKNOWN';

      console.log(`RunningHub任务 ${runningHubTaskId} 初始状态: ${currentStatus}`);

      // 如果有promptTipsData，检查是否有错误
      if (executeResult.promptTipsData) {
        const promptData = executeResult.promptTipsData;
        if (!promptData.result) {
          throw new Error(`RunningHub任务创建失败: ${promptData.error || '未知错误'}`);
        }
        console.log(`任务输出节点: ${promptData.outputs_to_execute?.join(', ') || '未知'}`);
      }

      // 如果任务还在运行中，等待完成
      if (currentStatus === 'RUNNING' || currentStatus === 'QUEUED' || currentStatus === 'PENDING') {
        console.log(`RunningHub任务 ${runningHubTaskId} 仍在执行中，开始轮询...`);

        const result = await runningHubService.waitForTaskCompletion(runningHubTaskId, {
          timeout: 300000, // 5分钟超时
          interval: 3000   // 3秒轮询间隔
        });

        if (!result.success) {
          throw new Error(`RunningHub任务执行失败: ${result.error}`);
        }

        // 获取任务结果
        const resultsResponse = await runningHubService.getTaskResults(runningHubTaskId);

        if (!resultsResponse.success) {
          throw new Error(`获取RunningHub任务结果失败: ${resultsResponse.error}`);
        }

        // 转换结果格式为统一格式
        return this.convertRunningHubResult(resultsResponse, workflowName);
      } else if (currentStatus === 'SUCCESS' || currentStatus === 'COMPLETED') {
        // 任务已完成，直接获取结果
        const resultsResponse = await runningHubService.getTaskResults(runningHubTaskId);

        if (!resultsResponse.success) {
          throw new Error(`获取RunningHub任务结果失败: ${resultsResponse.error}`);
        }

        return this.convertRunningHubResult(resultsResponse, workflowName);
      } else if (currentStatus === 'FAILED' || currentStatus === 'ERROR') {
        // 任务失败
        throw new Error(`RunningHub任务失败，状态: ${currentStatus}`);
      } else {
        // 未知状态，尝试查询当前状态
        console.log(`未知任务状态: ${currentStatus}，查询最新状态...`);

        const statusResult = await runningHubService.getTaskStatus(runningHubTaskId);

        if (!statusResult.success) {
          throw new Error(`获取RunningHub任务状态失败: ${statusResult.error}`);
        }

        // 递归调用，使用最新状态
        const updatedExecuteResult = {
          ...executeResult,
          taskStatus: statusResult.status
        };

        return this.processRunningHubResult(runningHubTaskId, workflowName, taskId, updatedExecuteResult);
      }

    } catch (error) {
      console.error('处理RunningHub结果失败:', error);
      throw error;
    }
  }

  /**
   * 处理ComfyUI结果
   * @param {string} promptId - 提示ID
   * @param {string} instanceId - 实例ID
   * @param {string} workflowName - 工作流名称
   * @param {Object} client - 客户端实例
   * @param {Object} executeResult - 执行结果
   * @returns {Promise<Object>} 处理结果
   */
  async processComfyUIResult(promptId, instanceId, workflowName, client, executeResult) {
    try {
      // 使用原有的ComfyUI结果处理逻辑
      return await this.comfyWorkflowService.processWorkflowResult(
        promptId, 
        instanceId, 
        workflowName, 
        client, 
        executeResult
      );
    } catch (error) {
      console.error('处理ComfyUI结果失败:', error);
      throw error;
    }
  }

  /**
   * 转换RunningHub结果为统一格式
   * @param {Object} runningHubResult - RunningHub结果
   * @param {string} workflowName - 工作流名称
   * @returns {Object} 统一格式结果
   */
  convertRunningHubResult(runningHubResult, workflowName) {
    console.log('转换RunningHub结果:', JSON.stringify(runningHubResult, null, 2));

    const result = {
      success: true,
      data: {
        outputs: {},
        platform: 'runninghub',
        taskId: runningHubResult.taskId
      }
    };

    // 处理结果数据
    const resultData = runningHubResult.data || runningHubResult.results || [];

    // 根据工作流类型转换结果
    if (workflowName.includes('extract') || workflowName.includes('matting')) {
      // 图片处理结果（抠图、取词等）
      if (Array.isArray(resultData) && resultData.length > 0) {
        result.data.outputs.images = resultData.map((item, index) => ({
          filename: item.filename || item.fileName || `result_${index}.png`,
          url: item.url || item.fileUrl || item.downloadUrl,
          ossUrl: item.url || item.fileUrl || item.downloadUrl,
          type: item.fileType || 'image',
          imageIndex: index,
          fileSize: item.fileSize,
          originalData: item
        }));
      }

      // 如果有文本提取结果
      if (runningHubResult.extractedText) {
        result.data.outputs.prompt = runningHubResult.extractedText;
      }
    } else {
      // 图片生成结果
      if (Array.isArray(resultData) && resultData.length > 0) {
        result.data.outputs.images = resultData.map((item, index) => ({
          filename: item.filename || item.fileName || `result_${index}.png`,
          url: item.url || item.fileUrl || item.downloadUrl,
          ossUrl: item.url || item.fileUrl || item.downloadUrl,
          type: item.fileType || 'image',
          imageIndex: index,
          fileSize: item.fileSize,
          originalData: item
        }));
      } else {
        // 如果没有结果数组，初始化空数组
        result.data.outputs.images = [];
      }
    }

    console.log('转换后的结果:', JSON.stringify(result, null, 2));
    return result;
  }
}

module.exports = UnifiedWorkflowService;
