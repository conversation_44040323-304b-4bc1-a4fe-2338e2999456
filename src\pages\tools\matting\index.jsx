import React, { useRef, useEffect, useState, useCallback } from 'react';
import { WORKFLOW_NAME } from '../../../data/workflowName';
import './index.css';
import {  MdClose } from 'react-icons/md';
import Masonry from 'masonry-layout';
import { Modal, Button, Spin, message} from 'antd';
import 'antd/dist/reset.css';
import { generateId, ID_TYPES } from '../../../utils/idGenerator';
import UploadGuideModal from '../../../components/UploadGuideModal';
import ImageInfoModal from '../../../components/ImageInfoModal';
import ImageDetailsModal from '../../../components/ImageDetailsModal';
import UploadBox from '../../../components/UploadBox';
import SourceImagePanel from '../../../components/SourceImagePanel';
import ControlPanel from '../../../components/ControlPanel';
import ResizeHandle from '../../../components/ResizeHandle';
import AutoUploadBox from '../../../components/AutoUploadBox';
import GenerationArea from '../../../components/GenerationArea';
import RequireLogin from '../../../components/RequireLogin';
import MaskDescriptionPanel, { tagMapping } from '../../../components/MaskDescriptionPanel';
import { getCurrentUserId } from '../../../api';
import { uploadFiles } from '../../../api/ossUpload';
import { executeFlow } from '../../../api/flow';
import { createFlowTask,checkUserBalance, updateFlowTask, getFlowTasks,getFlowTaskDetail,deleteFlowTask } from '../../../api/flowtask';
import { showDeleteConfirmModal } from '../../../utils/modalUtils';
import PromptIfUnsaved from '../../../components/PromptIfUnsaved';
import { useTaskContext } from '../../../contexts/TaskContext';


const MattingPage = ({ isLoggedIn, userId }) => {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const containerRef = useRef(null);
  const controlPanelRef = useRef(null);
  const generationAreaRef = useRef(null);
  const handleRef = useRef(null);
  const [activeTab, setActiveTab] = useState('result');
  const [controlPanelWidth, setControlPanelWidth] = useState(28);
  const showcaseRef = useRef(null);
  const masonryRef = useRef(null);
  const [showUploadGuide, setShowUploadGuide] = useState(false);
  const [uploadGuideType, setUploadGuideType] = useState('source');
  const [isProcessing, setIsProcessing] = useState(false);
  const [sourceImagePanels, setSourceImagePanels] = useState([]);
  const [backgroundPanels, setBackgroundPanels] = useState([]);
  const [showSourceUploadGuide, setShowSourceUploadGuide] = useState(false);
  const [operationsPanel, setOperationsPanel] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageDetails, setShowImageDetails] = useState(false);


  // 添加TaskContext的使用
  const { updateTask } = useTaskContext();

  // 添加控制面板标签状态，默认为"去背景"
  const [activePanelTab, setActivePanelTab] = useState('tab1');
  
  // 添加蒙版描述面板状态
  const [clothingMaskPanel, setClothingMaskPanel] = useState({
    componentId: generateId(ID_TYPES.COMPONENT),
    selectedTag: '',
    customText: '',
    description: ''
  });
  
  // 计算生成数量
  const getGenerationQuantity = () => {
    if (activePanelTab === 'tab1') {
      // 去背景标签页：根据上传的图片数量决定
      return getActivePanels().length;
    } else {
      // 抠衣服标签页：固定为1张
      return 1;
    }
  };
  
  // 任务列表状态
  const [generationTasks, setGenerationTasks] = useState([]);
  
  // 页面刷新/关闭提示
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）';
        return e.returnValue;
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);
  
  // 初始化和清理Masonry布局
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      // 销毁已存在的实例
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }

      // 创建新实例
      masonryRef.current = new Masonry(showcaseRef.current, {
        itemSelector: '.showcase-item',
        columnWidth: '.showcase-item',
        percentPosition: true,
        transitionDuration: '0.3s',
        initLayout: true,
        gutter: 16,
        fitWidth: false,
        horizontalOrder: true
      });

      // 监听窗口大小变化
      const handleResize = () => {
        if (masonryRef.current) {
          setTimeout(() => {
            masonryRef.current.layout();
          }, 100);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (masonryRef.current) {
          masonryRef.current.destroy();
          masonryRef.current = null;
        }
      };
    }
  }, [activeTab]);

  // 监听图片加载
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      const images = showcaseRef.current.getElementsByTagName('img');
      let loadedCount = 0;

      const handleImageLoad = () => {
        loadedCount++;
        if (masonryRef.current) {
          masonryRef.current.layout();
        }
      };

      Array.from(images).forEach(img => {
        if (img.complete) {
          handleImageLoad();
        } else {
          img.addEventListener('load', handleImageLoad);
        }
      });

      return () => {
        Array.from(images).forEach(img => {
          img.removeEventListener('load', handleImageLoad);
        });
      };
    }
  }, [activeTab]);

  // 获取当前活动标签页对应的面板数据
  const getActivePanels = () => {
    return activePanelTab === 'tab1' 
      ? backgroundPanels
      : sourceImagePanels;
  };
  
  // 获取当前活动标签页对应的setter函数
  const getActiveSetterFunction = () => {
    return activePanelTab === 'tab1' 
      ? setBackgroundPanels
      : setSourceImagePanels;
  };

  // 处理原始图片上传结果
  // 统一命名，按照指南标准
const handleUploadResult = (results) => {
    setHasUnsavedChanges(true);
    try {
      if (results.type === 'panels') {
        
        if (results.panels.length > 0) {
          // 确保所有面板都有正确的componentType和source属性
          const panelsWithType = results.panels.map(panel => ({
            ...panel,
            componentType: 'sourceImagePanel',  // 使用标准的componentType
            type: activePanelTab === 'tab1' ? 'background' : 'clothing', // 根据当前标签页设置业务类型
            source: 'upload' // 设置来源为用户上传
          }));
          
          // 获取当前面板数据和setter函数
          const activePanels = getActivePanels();
          const setActivePanels = getActiveSetterFunction();
          
          setActivePanels(prevPanels => [...prevPanels, ...panelsWithType]);
          
          // 延迟检查面板是否成功添加
          setTimeout(() => {
            const currentPanels = getActivePanels();
          }, 100);
        }
      } else if (results.type === 'update') {
        if (results.panel) {
          const updatedPanel = results.panel;
          const setActivePanels = getActiveSetterFunction();
          
          // 常规更新 - 更新对应ID的面板
          setActivePanels(prevPanels => 
            prevPanels.map(panel => {
              if (panel.componentId === updatedPanel.componentId) {
                // 保存重要字段
                console.log(`更新面板 ${panel.componentId}:`);
                console.log('- 原始状态:', panel.status);
                console.log('- 新状态:', updatedPanel.status);
                console.log('- processInfo:', updatedPanel.processInfo ? '有' : '无');
                console.log('- serverFileName:', updatedPanel.serverFileName || '未设置');
                
                // 返回更新后的面板，确保保留所有重要字段，特别是serverFileName和file属性
                return {
                  ...panel,
                  status: updatedPanel.status,
                  errorMessage: updatedPanel.errorMessage,
                  processedUrl: updatedPanel.processedUrl,
                  // 保存serverFileName - 关键修复
                  serverFileName: updatedPanel.serverFileName || panel.serverFileName,
                  // 保存processInfo
                  processInfo: updatedPanel.processInfo,
                  // 保留原始文件对象，如果存在
                  file: panel.file || updatedPanel.file
                };
              }
              return panel;
            })
          );
          
          // 延迟检查面板是否成功更新
          setTimeout(() => {
            const currentPanels = getActivePanels();
            const updatedPanelInState = currentPanels.find(p => p.componentId === updatedPanel.componentId);
            console.log('更新后的面板状态:', updatedPanelInState);
          }, 100);
        }
      } else if (results.type === 'error') {
        console.error('上传错误:', results.error);
        message.error('上传失败: ' + results.error);
        // 移除处理中的面板
        const setActivePanels = getActiveSetterFunction();
        setActivePanels(prevPanels => 
          prevPanels.filter(panel => panel.status !== 'processing'));
      }
    } catch (error) {
      console.error('上传结果处理失败:', error);
      message.error('上传结果处理失败: ' + (error.message || '未知错误'));
      // 移除处理中的面板
      const setActivePanels = getActiveSetterFunction();
      setActivePanels(prevPanels => 
        prevPanels.filter(panel => panel.status !== 'processing'));
    }
  };

  // 处理原始图片上传
  // 统一命名，按照指南标准
const handleFileUpload = (file) => {
};

  // 处理删除原始图片面板
  // 统一命名，按照指南标准
const handleDeleteImagePanel = (panelId) => {
    setHasUnsavedChanges(true);
    const setActivePanels = getActiveSetterFunction();
    setActivePanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
  };

  // 处理重新上传原始图片
  const handleReuploadSource = (panel) => {
    if (panel && panel.componentId) {
      // 第一步：删除当前面板
      handleDeleteImagePanel(panel.componentId);
      
      // 第二步：延迟一点点时间后触发上传区域点击（确保UI已更新）
      setTimeout(() => {
        // 直接触发上传区域的点击事件
        const uploadBox = document.getElementById('source-upload-box');
        if (uploadBox) {
          uploadBox.click();
        }
      }, 50);
    }
  };

  // 处理原始图片状态变化
  // 统一命名，按照指南标准
const handleImageStatusChange = (panelId, newStatus) => {
    const setActivePanels = getActiveSetterFunction();
    setActivePanels(prevPanels =>
      prevPanels.map(panel =>
        panel.componentId === panelId ? { ...panel, status: newStatus } : panel
      )
    );
  };

  // 处理开始生成按钮点击
  const handleGenerate = async () => {
    setIsProcessing(true);
    console.log('开始生成，准备数据');
    // 获取当前活动面板
    const activePanels = getActivePanels();
    // 验证是否有上传图片
    if (activePanels.length === 0) {
      message.error('请先上传原始图片');
      setIsProcessing(false);
      return;
    }

    // 如果是抠衣服标签页，先验证蒙版描述是否已填写
    if (activePanelTab === 'tab2') {
      // 检查蒙版描述组件是否有内容 (selectedTag或customText至少有一项填写)
      if ((!clothingMaskPanel.selectedTag || clothingMaskPanel.selectedTag.trim() === '') &&
          (!clothingMaskPanel.customText || clothingMaskPanel.customText.trim() === '') &&
          (!clothingMaskPanel.description || clothingMaskPanel.description.trim() === '')) {
        message.error('请选择款式标签或填写自定义描述');
        setIsProcessing(false);
        return;
      }
    }


    // 获取用户ID
    const userId = getCurrentUserId() || 'developer';
    
    // 创建任务ID
    const taskId = generateId(ID_TYPES.TASK);
    
    // 验证所有面板都有正确的serverFileName（包含扩展名）
    const invalidPanels = activePanels.filter(panel => 
      !panel.serverFileName || !panel.serverFileName.includes('.')
    );
    
    if (invalidPanels.length > 0) {
      console.error('缺少有效的文件名:', invalidPanels);
      
      // 详细打印所有面板的信息，帮助调试
      console.log('=== 全部面板信息 ===');
      activePanels.forEach((panel, index) => {
        console.log(`面板 ${index + 1}:`, {
          componentId: panel.componentId,
          serverFileName: panel.serverFileName,
          processInfo: panel.processInfo ? '存在' : '不存在',
          status: panel.status,
          allKeys: Object.keys(panel)
        });
        
        // 尝试从processInfo中查找文件名
        if (panel.processInfo) {
          console.log(`面板 ${index + 1} 的 processInfo:`, {
            originalFile: panel.processInfo.originalFile,
            processedFile: panel.processInfo.processedFile,
            relativePath: panel.processInfo.relativePath
          });
        }
      });
      
      // 输出原始状态对象，帮助查看sourceImagePanel的状态
      console.log('源面板状态:', sourceImagePanels);
      
      message.error('上传的图片缺少有效的文件引用，请重新上传图片');
      return;
    }
    // 检查用户余额
    if(activePanelTab === 'tab1'){
      const balance = await checkUserBalance('自动抠图', 'matting.tab1',activePanels.length);
      if(balance.code !== 200){
        message.error(balance.message);
setIsProcessing(false);
        return;
      } 
    }else{
      const balance = await checkUserBalance('自动抠图', 'matting.tab2',activePanels.length);
      if(balance.code !== 200){
        message.error(balance.message);
setIsProcessing(false);
        return;
      } 
    }
    
    // 如果是抠衣服标签页，验证蒙版描述是否已填写
    if (activePanelTab === 'tab2') {
      // 检查蒙版描述组件是否有内容 (selectedTag或customText至少有一项填写)
      if ((!clothingMaskPanel.selectedTag || clothingMaskPanel.selectedTag.trim() === '') && 
          (!clothingMaskPanel.customText || clothingMaskPanel.customText.trim() === '') &&
          (!clothingMaskPanel.description || clothingMaskPanel.description.trim() === '')) {
        message.error('请选择款式标签或填写自定义描述');
        setIsProcessing(false);
        return;
      }
    }
    // 获取所有文件上传后端
    // 处理从TaskPanel拖拽过来的图片，确保所有图片都有file对象
    const filesToUpload = [];
    const processedPanels = [];
    
    for (const panel of activePanels) {
      let fileToUpload = panel.file;
      
      // 如果没有file对象但有URL，需要从URL获取文件
      if (!fileToUpload && panel.url) {
        try {
          const response = await fetch(panel.url);
          const blob = await response.blob();
          fileToUpload = new File([blob], panel.serverFileName || 'image.jpg', {
            type: blob.type || 'image/jpeg'
          });
        } catch (error) {
          console.error('从URL获取文件失败:', error);
          message.error('图片处理失败，请重试');
          setIsProcessing(false);
          return;
        }
      }
      
      if (fileToUpload) {
        filesToUpload.push(fileToUpload);
        processedPanels.push(panel);
      } else {
        console.error('无法获取文件:', panel);
        message.error('图片文件获取失败，请重新上传');
        setIsProcessing(false);
        return;
      }
    }
    
    if (filesToUpload.length === 0) {
      message.error('没有有效的图片文件');
      setIsProcessing(false);
      return;
    }
    
    const {urls, fileInfos} = await uploadFiles(filesToUpload);
// 收集所有必要的数据
    const taskData = {
      taskId: taskId,
      userId: userId,
      createdAt: new Date(),
      status: 'processing',
      pageType: 'matting',
      // 使用新的组件结构 - 使用数组而不是对象
      // 包含所有上传的图片，不只是第一张
      components: processedPanels.map((panel, index) => {
        console.log(fileInfos[index]);
        return {
          componentType: 'sourceImagePanel',  // 使用标准字段名
          componentId: generateId(ID_TYPES.COMPONENT), // 始终生成新的组件ID
          serverFileName: panel.serverFileName, // 使用正确的serverFileName
          name: `原始图片 #${index + 1}`,
          status: 'completed',
          originalUrl: urls[index] || panel.processedFile,
          fileInfo: fileInfos[index] || {},
          originalImage: panel.originalImage || panel.url,
          isMainImage: index === 0 // 只有第一张标记为主图片
        };
      }),
    };
    // 如果是抠衣服标签页，添加蒙版描述组件
    if (activePanelTab === 'tab2') {
      // 添加蒙版描述组件到任务组件数组中
      taskData.components.push({
        componentType: 'maskDescriptionPanel',
        componentId: clothingMaskPanel.componentId,
        name: '款式描述',
        selectedTag: clothingMaskPanel.selectedTag,
        customText: clothingMaskPanel.customText,
        description: clothingMaskPanel.description
      });
    }
    
      
    // 创建任务
    const res = await createFlowTask(taskData)
    console.log(res)
    // 记录组件信息
    console.log('\n==== 任务结构信息 ====');
    console.log('组件结构:', taskData.components);
    console.log('上传的图片数量:', processedPanels.length);
    
    // 添加到本地状态并切换到结果标签页
    if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
    setActiveTab('result');
    
    try {
      // 根据图片数量决定使用哪个工作流
      // 如果有多个图片，使用mattingbgfile工作流；如果只有一个图片，使用mattingbg工作流
      const workflowType = activePanelTab === 'tab1' 
        ? (processedPanels.length > 1 ? 'mattingbgfile' : 'mattingbg')  // 去背景标签页，rmbgfile改为mattingbgfile
        : 'mattingclo';  // 抠衣服标签页始终使用单张图片处理工作流
      
      console.log(`选择工作流: ${workflowType}，处理 ${processedPanels.length} 张图片`);
      
      // 设置页面特定的参数
      const pageSpecificData = {
        generation: {
          count: activePanelTab === 'tab2' ? 1 : processedPanels.length, // 抠衣服标签页只处理一张图片
          prompt: activePanelTab === 'tab2' ? '将图片进行衣物抠图处理' : '将图片进行背景去除处理'
        }
      };
      
      // 在抠衣服标签页时，添加蒙版描述
      if (activePanelTab === 'tab2') {
        // 只有当蒙版描述不为空时才添加
        if (clothingMaskPanel.description && clothingMaskPanel.description.trim() !== '') {
          pageSpecificData.generation.maskDescription = clothingMaskPanel.description;
        }
      }
      
      // 使用工厂函数准备任务数据
      console.log('\n==== 调用createPageTask API ====');
      let result = {};
      if(activePanelTab === 'tab1'){
        if(urls.length > 1){
            result = await executeFlow(WORKFLOW_NAME.MATTING_BG,{
              "53": {
                  "url":urls.join('\n')
            },
            "subInfo":{
              "type": "matting.tab1",
              "title":"自动抠图",
              "count":activePanels.length
            }
          },taskData.taskId);
          setIsProcessing(false);
          setHasUnsavedChanges(false);
        }else{
          result = await executeFlow(WORKFLOW_NAME.MATTING_BG,{
              "53": {
                "url":urls[0]
            },
            "subInfo":{
              "type": "matting.tab1",
              "title":"自动抠图",
              "count":activePanels.length
            }
          },taskData.taskId);
          setIsProcessing(false);
          setHasUnsavedChanges(false);
        }
      }else{  
          result = await executeFlow(WORKFLOW_NAME.MATTING_CLO,{
            "58": {
              "url": urls.join('\n')
            },  
            "54": {
              "prompt":clothingMaskPanel.description
            },
            "subInfo":{
              "type": "matting.tab2",
              "title":"自动抠图",
              "count":activePanels.length
            }
        },taskData.taskId);
          setIsProcessing(false);
          setHasUnsavedChanges(false);
      }
      if( generationAreaRef.current){
        taskData.promptId = result.promptId;
        taskData.instanceId = result.instanceId;
        taskData.url = result.url;
        taskData.newTask = true;
        generationAreaRef.current.setGenerationTasks(taskData);
    }
    } catch (error) {
      // 更新任务失败
      console.error('API调用失败:', error);
      setIsProcessing(false);
      setHasUnsavedChanges(false);
      setIsProcessing(false);
      message.error('创建任务失败: ' + (error.message || '未知错误'));
      
      // 更新任务状态为失败并触发提示音
      taskData.status = 'failed';
      taskData.errorMessage = error.message || '未知错误';
      if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      
      // 调用updateTask以触发失败提示音
      updateTask(taskData);
    }
  };
  
  // 恢复任务设置（编辑现有任务）
  const handleEditTask = (task) => {
    // 打印task信息
    console.log('正在恢复任务设置', task);
    
    try {
      if (!task) {
        console.error('传入的任务对象为空');
        message.error('任务数据为空，无法编辑');
        return;
      }
      
      // 只处理数组格式的组件
      const components = Array.isArray(task.components) ? task.components : [];
      console.log('处理的组件数据:', components);
      
      // 获取源图像组件 - 匹配小写组件名
      const sourceImageComponent = components.find(c => c.componentType === 'sourceImagePanel');
      
      if (sourceImageComponent) {
        console.log('获取到源图像组件:', sourceImageComponent);
        
        const enhancedSource = {
          ...sourceImageComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          url: sourceImageComponent.url,
          originalImage: sourceImageComponent.originalImage,
          status: 'completed'
        };
        
        const setActivePanels = getActiveSetterFunction();
        setActivePanels([enhancedSource]);
        
        console.log('已恢复组件数据:', enhancedSource);
      } else {
        console.error('未找到源图像组件，无法显示详情');
        message.error('无法加载图片详情：源图像组件数据缺失');
        return;
      }
      
      // 恢复蒙版描述组件数据 - 如果是tab2标签页
      if (activePanelTab === 'tab2') {
        const maskDescriptionComponent = components.find(c => c.componentType === 'maskDescriptionPanel');
        if (maskDescriptionComponent) {
          console.log('获取到蒙版描述组件:', maskDescriptionComponent);
          // 恢复蒙版描述面板数据
          setClothingMaskPanel({
            componentId: generateId(ID_TYPES.COMPONENT),
            selectedTag: maskDescriptionComponent.selectedTag || '',
            customText: maskDescriptionComponent.customText || '',
            description: maskDescriptionComponent.description || ''
          });
        } else {
          console.log('未找到蒙版描述组件，使用空值');
          // 重置蒙版描述面板
          setClothingMaskPanel({
            componentId: generateId(ID_TYPES.COMPONENT),
            selectedTag: '',
            customText: '',
            description: ''
          });
        }
      }
      
      // 切换到设置页
      setActiveTab('settings');
      
      message.success('成功恢复任务设置');
      
    } catch (error) {
      console.error('恢复任务设置时出错:', error);
      message.error('恢复任务设置失败: ' + (error.message || '未知错误'));
    }
  };

  // 处理查看详情
  const handleViewDetails = (image, task, imageIndex = 0) => {
    // 获取任务ID
    const taskId = task.taskId;
    if (!taskId) {
      message.error('无法获取任务详情：任务ID无效');
      return;
    }
    // 显示加载状态
    message.loading({ content: '正在加载任务详情...', key: 'loadingDetails' });
    if (!task) {
      message.error({ content: '无法加载任务详情', key: 'loadingDetails' });
      return;
    }
      // 获取主图片组件
      const mainImageComponent = task.components[imageIndex]
    
      // 准备组件数据 - 转为数组格式
      const adaptedComponents = [
        {
          ...mainImageComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'sourceImagePanel',
          title: mainImageComponent.name || '原始图片',
          originalImage: mainImageComponent.originalUrl,
          url: mainImageComponent.url,
        }
      ];

      // 查找蒙版描述组件并添加到组件列表中
      const maskDescriptionComponent = task.components.find(c => c.componentType === 'maskDescriptionPanel');
      if (maskDescriptionComponent) {
        adaptedComponents.push({
          ...maskDescriptionComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'maskDescriptionPanel'
        });
      }

    const detailInfo = {
      url: task.processInfo.results[imageIndex].url,
      taskId: task.taskId,
      createdAt: task.createdAt,
      components: adaptedComponents,
      imageIndex: imageIndex,
      _dataSource: 'database'
    }
    console.log('generatedImage',detailInfo );
    return detailInfo;
  }

  // 添加关闭弹窗时的处理函数
  const handleCloseImageDetails = () => {
    // 直接关闭弹窗，不使用动画
    setShowImageDetails(false);
    
    // 重置状态，无需延迟
    setSelectedImage(null);
    

 
  };

  // 处理蒙版描述面板变化
  const handleMaskPanelChange = (panel) => {
    if (panel) {
      setClothingMaskPanel(panel);
      setHasUnsavedChanges(true);
    }
  };

  return (
    <RequireLogin isLoggedIn={isLoggedIn} featureName="自动抠图功能">
      <PromptIfUnsaved when={hasUnsavedChanges} message="操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）" />
      <div className="matting-page">
        <div className="matting-container" ref={containerRef}>
          <ControlPanel
            ref={controlPanelRef}
            width={`${controlPanelWidth}%`}
            onGenerate={handleGenerate}
            disabled={isProcessing}
            featureName="matting"
            quantity={getGenerationQuantity()}
            activeTab={activePanelTab}
            onTabChange={setActivePanelTab}
            tabs={[
              { key: 'tab1', label: '去背景' },
              { key: 'tab2', label: '抠衣服' }
            ]}
          >
                    {/* 操作区标签页内容 */}
                    {activePanelTab === 'tab1' && (
                      <>
                        {/* 原始图片上传区域 - 始终显示，除非达到20张上限 */}
                        {getActivePanels().length < 20 && (
                          <AutoUploadBox
                            id="source-upload-box"
                            onUpload={handleFileUpload}
                            supportMultiple={true}
                            onShowGuide={() => {
                              setUploadGuideType('source');
                              setShowSourceUploadGuide(true);
                            }}
                            paramName="53"
                            onUploadResult={handleUploadResult}
                            panels={getActivePanels()}
                            className="mt-2"
                            showSupportTag={false}
                            pageType="matting"
                            uploadType="source"
                            uploadSubType="tab1"
                          />
                        )}
                        
                        {/* 展示原图面板 */}
                        {getActivePanels().map((panel, index) => (
                          <SourceImagePanel
                            key={panel.componentId}
                            panel={{
                              ...panel,
                              // 只在matting页面为原始图片添加序号
                              title: `原始图片 #${index + 1}`
                            }}
                            onExpandClick={(panel, position) => {
                              // 创建增强的面板对象，避免警告
                              const enhancedPanel = {
                                ...panel,
                                // 标记为增强的原始图片面板以避免不必要的警告
                                isEnhancedSourceImagePanel: true, // 添加原始图片标记
                                processedFile: panel.processedFile || panel.preview || panel.url || panel.image,
                                fileInfo: panel.fileInfo || {
                                  size: 500000, // 默认500KB
                                  format: 'image/jpeg',
                                  type: 'image/jpeg',
                                  width: 800,
                                  height: 1200
                                }
                              };
                              
                              setOperationsPanel({
                                panel: enhancedPanel,
                                position
                              });
                            }}
                            onDelete={() => handleDeleteImagePanel(panel.componentId)}
                            onReupload={() => handleReuploadSource(panel)}
                            onStatusChange={(newStatus) => handleImageStatusChange(panel.componentId, newStatus)}
                            isActive={panel.status === 'completed'}
                            onPanelsChange={getActiveSetterFunction()}
                            pageType="matting"
                          />
                        ))}
                      </>
                    )}
                    
                    {/* 抠衣服标签页内容 - 与操作区内容相同，但使用不同的面板数据 */}
                    {activePanelTab === 'tab2' && (
                      <>
                        {/* 原始图片上传区域 - 抠衣服标签页只允许上传一张图片 */}
                        {getActivePanels().length < 1 && (
                          <UploadBox
                            id="source-upload-box"
                            onUpload={handleFileUpload}
                            onShowGuide={() => {
                              setUploadGuideType('source');
                              setShowSourceUploadGuide(true);
                            }}
                            onUploadResult={handleUploadResult}
                            onChange={() => {}}
                            panels={getActivePanels()}
                            className="mt-2"
                            showSupportTag={false}
                            pageType="matting"
                            uploadType="source"
                            uploadSubType="tab2"
                          />
                        )}
                        
                        {/* 展示原图面板 */}
                        {getActivePanels().map((panel, index) => (
                          <SourceImagePanel
                            key={panel.componentId}
                            panel={{
                              ...panel,
                              // 只在matting页面为原始图片添加序号
                              title: `原始图片 #${index + 1}`
                            }}
                            onExpandClick={(panel, position) => {
                              // 创建增强的面板对象，避免警告
                              const enhancedPanel = {
                                ...panel,
                                // 标记为增强的原始图片面板以避免不必要的警告
                                isEnhancedSourceImagePanel: true, // 添加原始图片标记
                                processedFile: panel.processedFile || panel.preview || panel.url || panel.image,
                                fileInfo: panel.fileInfo || {
                                  size: 500000, // 默认500KB
                                  format: 'image/jpeg',
                                  type: 'image/jpeg',
                                  width: 800,
                                  height: 1200
                                }
                              };
                              
                              setOperationsPanel({
                                panel: enhancedPanel,
                                position
                              });
                            }}
                            onDelete={() => handleDeleteImagePanel(panel.componentId)}
                            onReupload={() => handleReuploadSource(panel)}
                            onStatusChange={(newStatus) => handleImageStatusChange(panel.componentId, newStatus)}
                            isActive={panel.status === 'completed'}
                            onPanelsChange={getActiveSetterFunction()}
                            pageType="matting"
                          />
                        ))}
                        
                        {/* 蒙版描述面板 - 当有图片上传时显示 */}
                        {getActivePanels().length > 0 && (
                          <MaskDescriptionPanel
                            panel={clothingMaskPanel}
                            onPanelsChange={handleMaskPanelChange}
                          />
                        )}
                      </>
                    )}
          </ControlPanel>

          <ResizeHandle
            ref={handleRef}
            containerRef={containerRef}
            onResize={setControlPanelWidth}
            minWidth={25}
            maxWidth={50}
          />

                  <GenerationArea
            ref={generationAreaRef}    setIsProcessing={setIsProcessing}

            activeTab={activeTab}
            onTabChange={setActiveTab}
            onEditTask={handleEditTask}
            onViewDetails={handleViewDetails}
            pageType="matting"
          />
        </div>

        {/* 上传指南模态框 */}
        {showUploadGuide && (
          <UploadGuideModal
            type={uploadGuideType}
            pageType="matting"
            onClose={() => setShowUploadGuide(false)}
            onUpload={(result) => {
              console.log('收到上传结果:', result);
              // 根据结果中的shouldClose字段决定是否关闭弹窗
              if (result.shouldClose !== false) {
                setShowUploadGuide(false);
              }
            }}
          />
        )}

        {/* 原始图片上传指导弹窗 */}
        {showSourceUploadGuide && (
          <UploadGuideModal
            type="source"
            pageType="matting"
            onClose={() => setShowSourceUploadGuide(false)}
            onUpload={(result) => {
              console.log('收到原始图片上传结果:', result);
              handleUploadResult(result);
              
              // 根据结果中的shouldClose字段决定是否关闭弹窗
              if (result.shouldClose !== false) {
                setShowSourceUploadGuide(false);
              }
            }}
          />
        )}

        {/* 添加操作弹窗 */}
        {operationsPanel && (
          <ImageInfoModal
            panel={operationsPanel.panel}
            position={operationsPanel.position}
            onClose={() => setOperationsPanel(null)}
            onDelete={() => handleDeleteImagePanel(operationsPanel.panel.componentId)}
            onReupload={() => handleReuploadSource(operationsPanel.panel)}
            pageType="matting"
          />
        )}



        {/* 显示生成中的加载状态 - 改为绝对定位在右下角，不影响布局 */}
        {isProcessing && (
          <div className="generating-status">
            <Spin size="small" />
            <p>处理中...</p>
          </div>
        )}
      </div>
    </RequireLogin>
  );
};

export default MattingPage; 