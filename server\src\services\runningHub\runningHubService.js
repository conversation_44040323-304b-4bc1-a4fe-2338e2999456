/**
 * RunningHub服务 - 服务器端
 * 提供基于RunningHub平台的AI任务处理服务
 */

const axios = require('axios');
const FormData = require('form-data');
const { createError } = require('../../utils/error');

// RunningHub API配置
const RUNNING_HUB_CONFIG = {
  baseURL: 'https://www.runninghub.cn',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Host': 'www.runninghub.cn'
  }
};

// 创建axios实例
const runningHubAxios = axios.create(RUNNING_HUB_CONFIG);

/**
 * RunningHub服务类
 */
class RunningHubService {
  constructor() {
    this.apiKey = null;
    this.workflowId = null;
  }

  /**
   * 设置API密钥
   * @param {string} apiKey - RunningHub API密钥
   */
  setApiKey(apiKey) {
    this.apiKey = apiKey;
  }

  /**
   * 设置工作流ID
   * @param {string} workflowId - 工作流ID
   */
  setWorkflowId(workflowId) {
    this.workflowId = workflowId;
  }

  /**
   * 创建简易ComfyUI任务
   * @param {Object} options - 任务选项
   * @returns {Promise<Object>} 任务创建结果
   */
  async createSimpleTask(options = {}) {
    try {
      const apiKey = options.apiKey || this.apiKey;
      const workflowId = options.workflowId || this.workflowId;

      if (!apiKey || !workflowId) {
        throw createError(400, 'API密钥和工作流ID是必需的');
      }

      const requestData = {
        apiKey,
        workflowId,
        ...(options.addMetadata !== undefined && { addMetadata: options.addMetadata })
      };

      const response = await runningHubAxios.post('/task/openapi/create', requestData);

      if (response.data.code !== 0) {
        throw createError(500, response.data.msg || '创建任务失败');
      }

      return {
        success: true,
        data: response.data.data,
        taskId: response.data.data.taskId,
        taskStatus: response.data.data.taskStatus,
        netWssUrl: response.data.data.netWssUrl,
        clientId: response.data.data.clientId,
        promptTips: response.data.data.promptTips
      };
    } catch (error) {
      console.error('创建RunningHub简易任务失败:', error);
      throw error.status ? error : createError(500, `创建任务失败: ${error.message}`);
    }
  }

  /**
   * 创建高级ComfyUI任务
   * @param {Object} options - 任务选项
   * @returns {Promise<Object>} 任务创建结果
   */
  async createAdvancedTask(options = {}) {
    try {
      const apiKey = options.apiKey || this.apiKey;
      const workflowId = options.workflowId || this.workflowId;

      if (!apiKey || !workflowId) {
        throw createError(400, 'API密钥和工作流ID是必需的');
      }

      const requestData = {
        apiKey,
        workflowId,
        ...(options.nodeInfoList && { nodeInfoList: options.nodeInfoList })
      };

      const response = await runningHubAxios.post('/task/openapi/create', requestData);

      if (response.data.code !== 0) {
        throw createError(500, response.data.msg || '创建高级任务失败');
      }

      // 解析promptTips中的信息
      let promptTipsData = null;
      try {
        if (response.data.data.promptTips) {
          promptTipsData = JSON.parse(response.data.data.promptTips);
        }
      } catch (error) {
        console.warn('解析promptTips失败:', error);
      }

      return {
        success: true,
        data: response.data.data,
        taskId: response.data.data.taskId,
        taskStatus: response.data.data.taskStatus,
        netWssUrl: response.data.data.netWssUrl,
        clientId: response.data.data.clientId,
        promptTips: response.data.data.promptTips,
        promptTipsData: promptTipsData,
        // 添加用于异步处理的信息
        platform: 'runninghub',
        url: 'https://www.runninghub.cn',
        runningHubService: this // 传递服务实例用于后续操作
      };
    } catch (error) {
      console.error('创建RunningHub高级任务失败:', error);
      throw error.status ? error : createError(500, `创建高级任务失败: ${error.message}`);
    }
  }

  /**
   * 创建AI应用任务
   * @param {Object} options - 任务选项
   * @returns {Promise<Object>} 任务创建结果
   */
  async createAIAppTask(options = {}) {
    try {
      const apiKey = options.apiKey || this.apiKey;

      if (!apiKey || !options.appId) {
        throw createError(400, 'API密钥和应用ID是必需的');
      }

      const requestData = {
        apiKey,
        appId: options.appId,
        inputData: options.inputData || {}
      };

      const response = await runningHubAxios.post('/task/openapi/app/create', requestData);

      if (response.data.code !== 0) {
        throw createError(500, response.data.msg || '创建AI应用任务失败');
      }

      return {
        success: true,
        data: response.data.data,
        taskId: response.data.data.taskId,
        taskStatus: response.data.data.taskStatus
      };
    } catch (error) {
      console.error('创建RunningHub AI应用任务失败:', error);
      throw error.status ? error : createError(500, `创建AI应用任务失败: ${error.message}`);
    }
  }

  /**
   * 查询任务状态
   * @param {string} taskId - 任务ID
   * @param {string} apiKey - API密钥（可选）
   * @returns {Promise<Object>} 任务状态
   */
  async getTaskStatus(taskId, apiKey = null) {
    try {
      const key = apiKey || this.apiKey;
      if (!key) {
        throw createError(400, 'API密钥是必需的');
      }

      const response = await runningHubAxios.post('/task/openapi/status', {
        apiKey: key,
        taskId
      });

      if (response.data.code !== 0) {
        throw createError(500, response.data.msg || '查询任务状态失败');
      }

      return {
        success: true,
        status: response.data.data,
        taskId
      };
    } catch (error) {
      console.error('查询RunningHub任务状态失败:', error);
      throw error.status ? error : createError(500, `查询任务状态失败: ${error.message}`);
    }
  }

  /**
   * 获取任务生成结果
   * @param {string} taskId - 任务ID
   * @param {string} apiKey - API密钥（可选）
   * @returns {Promise<Object>} 任务结果
   */
  async getTaskResults(taskId, apiKey = null) {
    try {
      const key = apiKey || this.apiKey;
      if (!key) {
        throw createError(400, 'API密钥是必需的');
      }

      const response = await runningHubAxios.post('/task/openapi/outputs', {
        apiKey: key,
        taskId
      });

      if (response.data.code !== 0) {
        throw createError(500, response.data.msg || '获取任务结果失败');
      }

      return {
        success: true,
        data: response.data.data,
        taskId,
        results: response.data.data || []
      };
    } catch (error) {
      console.error('获取RunningHub任务结果失败:', error);
      throw error.status ? error : createError(500, `获取任务结果失败: ${error.message}`);
    }
  }

  /**
   * 取消ComfyUI任务
   * @param {string} taskId - 任务ID
   * @param {string} apiKey - API密钥（可选）
   * @returns {Promise<Object>} 取消结果
   */
  async cancelTask(taskId, apiKey = null) {
    try {
      const key = apiKey || this.apiKey;
      if (!key) {
        throw createError(400, 'API密钥是必需的');
      }

      const response = await runningHubAxios.post('/task/openapi/cancel', {
        apiKey: key,
        taskId
      });

      if (response.data.code !== 0) {
        throw createError(500, response.data.msg || '取消任务失败');
      }

      return {
        success: true,
        data: response.data.data,
        taskId
      };
    } catch (error) {
      console.error('取消RunningHub任务失败:', error);
      throw error.status ? error : createError(500, `取消任务失败: ${error.message}`);
    }
  }

  /**
   * 获取账户信息
   * @param {string} apiKey - API密钥（可选）
   * @returns {Promise<Object>} 账户信息
   */
  async getAccountInfo(apiKey = null) {
    try {
      const key = apiKey || this.apiKey;
      if (!key) {
        throw createError(400, 'API密钥是必需的');
      }

      const response = await runningHubAxios.post('/task/openapi/account', {
        apiKey: key
      });

      if (response.data.code !== 0) {
        throw createError(500, response.data.msg || '获取账户信息失败');
      }

      return {
        success: true,
        data: response.data.data
      };
    } catch (error) {
      console.error('获取RunningHub账户信息失败:', error);
      throw error.status ? error : createError(500, `获取账户信息失败: ${error.message}`);
    }
  }

  /**
   * 获取工作流JSON
   * @param {string} workflowId - 工作流ID
   * @param {string} apiKey - API密钥（可选）
   * @returns {Promise<Object>} 工作流JSON
   */
  async getWorkflowJson(workflowId, apiKey = null) {
    try {
      const key = apiKey || this.apiKey;
      const wfId = workflowId || this.workflowId;

      if (!key || !wfId) {
        throw createError(400, 'API密钥和工作流ID是必需的');
      }

      const response = await runningHubAxios.post('/task/openapi/workflow', {
        apiKey: key,
        workflowId: wfId
      });

      if (response.data.code !== 0) {
        throw createError(500, response.data.msg || '获取工作流JSON失败');
      }

      return {
        success: true,
        data: response.data.data,
        workflowId: wfId
      };
    } catch (error) {
      console.error('获取RunningHub工作流JSON失败:', error);
      throw error.status ? error : createError(500, `获取工作流JSON失败: ${error.message}`);
    }
  }

  /**
   * 上传资源文件
   * @param {Buffer|File} file - 文件数据
   * @param {string} apiKey - API密钥（可选）
   * @returns {Promise<Object>} 上传结果
   */
  async uploadResource(file, apiKey = null) {
    try {
      const key = apiKey || this.apiKey;
      if (!key) {
        throw createError(400, 'API密钥是必需的');
      }

      const formData = new FormData();
      formData.append('apiKey', key);
      formData.append('file', file);

      const response = await runningHubAxios.post('/task/openapi/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.data.code !== 0) {
        throw createError(500, response.data.msg || '上传资源失败');
      }

      return {
        success: true,
        data: response.data.data,
        fileUrl: response.data.data.fileUrl
      };
    } catch (error) {
      console.error('上传RunningHub资源失败:', error);
      throw error.status ? error : createError(500, `上传资源失败: ${error.message}`);
    }
  }

  /**
   * 批量创建任务
   * @param {Array} taskList - 任务列表
   * @returns {Promise<Object>} 批量创建结果
   */
  async createBatchTasks(taskList) {
    try {
      if (!Array.isArray(taskList) || taskList.length === 0) {
        throw createError(400, '任务列表不能为空');
      }

      const results = [];

      for (const taskOptions of taskList) {
        try {
          let result;
          if (taskOptions.type === 'simple') {
            result = await this.createSimpleTask(taskOptions.params);
          } else if (taskOptions.type === 'advanced') {
            result = await this.createAdvancedTask(taskOptions.params);
          } else if (taskOptions.type === 'app') {
            result = await this.createAIAppTask(taskOptions.params);
          } else {
            throw new Error(`不支持的任务类型: ${taskOptions.type}`);
          }

          results.push({
            index: results.length,
            success: result.success,
            taskId: result.taskId,
            data: result
          });
        } catch (error) {
          results.push({
            index: results.length,
            success: false,
            error: error.message,
            taskOptions
          });
        }
      }

      return {
        success: true,
        results,
        totalTasks: taskList.length,
        successCount: results.filter(r => r.success).length,
        failureCount: results.filter(r => !r.success).length
      };
    } catch (error) {
      console.error('批量创建RunningHub任务失败:', error);
      throw error.status ? error : createError(500, `批量创建任务失败: ${error.message}`);
    }
  }

  /**
   * 等待任务完成
   * @param {string} taskId - 任务ID
   * @param {Object} options - 选项
   * @param {number} options.timeout - 超时时间（毫秒）
   * @param {number} options.interval - 轮询间隔（毫秒）
   * @param {Function} options.onProgress - 进度回调
   * @returns {Promise<Object>} 任务结果
   */
  async waitForTaskCompletion(taskId, options = {}) {
    const {
      timeout = 300000, // 5分钟默认超时
      interval = 3000,  // 3秒轮询间隔
      onProgress = null
    } = options;

    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      try {
        const statusResult = await this.getTaskStatus(taskId);

        if (!statusResult.success) {
          throw new Error(statusResult.error || '获取任务状态失败');
        }

        const status = statusResult.status;

        // 触发进度回调
        if (onProgress) {
          onProgress({
            taskId,
            status,
            elapsed: Date.now() - startTime
          });
        }

        // 检查任务是否完成
        if (status === 'SUCCESS') {
          const results = await this.getTaskResults(taskId);
          return {
            success: true,
            status: 'SUCCESS',
            results: results.results || [],
            elapsed: Date.now() - startTime
          };
        } else if (status === 'FAILED') {
          return {
            success: false,
            status: 'FAILED',
            error: '任务执行失败',
            elapsed: Date.now() - startTime
          };
        }

        // 等待下次轮询
        await new Promise(resolve => setTimeout(resolve, interval));

      } catch (error) {
        console.error('等待RunningHub任务完成失败:', error);
        throw error.status ? error : createError(500, `等待任务完成失败: ${error.message}`);
      }
    }

    // 超时
    throw createError(408, '任务执行超时');
  }
}

// 导出类和默认实例
module.exports = RunningHubService;
module.exports.default = new RunningHubService();
