import React, { useRef, useEffect, useState, useCallback, useLayoutEffect, Suspense, memo } from 'react';
import './index.css';
import { MdOutlineZoomOutMap, MdOutlineAutoAwesome, MdClose, MdOutlineDescription } from 'react-icons/md';
import { CloseOutlined } from '@ant-design/icons';
import Masonry from 'masonry-layout';
import { Modal, message, Button, Spin, Input, Tabs } from 'antd';
import 'antd/dist/reset.css';
import '../../../styles/antd-override.css';
import '../../../styles/panels.css'; // 导入统一面板样式
import { generateId, ID_TYPES } from '../../../utils/idGenerator';
import UploadGuideModal from '../../../components/UploadGuideModal';
import ImageInfoModal from '../../../components/ImageInfoModal';
import UploadBox from '../../../components/UploadBox';
import RandomSeedSelector from '../../../components/RandomSeedSelector';
import ImageSizeSelector from '../../../components/ImageSizeSelector';
import QuantityPanel from '../../../components/QuantityPanel';
import ImageDetailsModal from '../../../components/ImageDetailsModal';
import ControlPanel from '../../../components/ControlPanel';
import ResizeHandle from '../../../components/ResizeHandle';
import GenerationArea from '../../../components/GenerationArea';
import RequireLogin from '../../../components/RequireLogin';
import TextDescriptionPanel from '../../../components/TextDescriptionPanel';
import ReferencePanel from '../../../components/ReferencePanel'; // 导入参考图面板组件
import WeightPanel from '../../../components/WeightPanel'; // 导入强度调节面板组件
import { getCurrentUserId } from '../../../api';
import { executeFlow } from '../../../api/flow';
import { createFlowTask, updateFlowTask, getFlowTasks, getFlowTaskDetail, deleteFlowTask,checkUserBalance } from '../../../api/flowtask';
import { uploadFiles } from '../../../api/ossUpload';
import { WORKFLOW_NAME } from '../../../data/workflowName';
import { getImageSize } from 'react-image-size';
import PromptIfUnsaved from '../../../components/PromptIfUnsaved';
import { useTaskContext } from '../../../contexts/TaskContext';

const MemoizedImageDetailsModal = React.memo(ImageDetailsModal);

// 添加提示信息常量
const FASHION_TIP = '此功能可以根据您上传的参考图片和描述词生成时尚大片效果。为获得最佳效果，请上传清晰的参考图片，并提供详细的描述词。';

const FashionPage = ({ isLoggedIn, userId }) => {
  // 常量定义
  const UPLOAD_FIELD_ID = 'upload-field';
  const CONTROL_PANEL_DEFAULT_WIDTH = 28; // 默认控制面板宽度（百分比）
  const generationAreaRef = useRef(null);
  // 参考useState和useRef hooks
  const containerRef = useRef(null);
  const controlPanelRef = useRef(null);
  const handleRef = useRef(null);
  const showcaseRef = useRef(null);
  const masonryRef = useRef(null);
  const [activeTab, setActiveTab] = useState('result');
  const [controlPanelWidth, setControlPanelWidth] = useState(CONTROL_PANEL_DEFAULT_WIDTH);
  const [showUploadGuide, setShowUploadGuide] = useState(false);
  const [uploadGuideFiles, setUploadGuideFiles] = useState([]);
  const [uploadGuideGalleryMode, setUploadGuideGalleryMode] = useState(false);
  const [processedImages, setProcessedImages] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [referencePanels, setReferencePanels] = useState([]);
  const [operationsPanel, setOperationsPanel] = useState(null);
  const [imageQuantity, setImageQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageDetails, setShowImageDetails] = useState(false);
  const [showAdvancedText, setShowAdvancedText] = useState(false);

  const [hasCustomAdvancedPrompt, setHasCustomAdvancedPrompt] = useState(false);
  const [textDescription, setTextDescription] = useState('');
  const [editingTaskId, setEditingTaskId] = useState(null);
  const [taskDetailsData, setTaskDetailsData] = useState(null);
  const [showTaskDetails, setShowTaskDetails] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const lastPosition = useRef({ x: 0, y: 0 });
  const [imageScale, setImageScale] = useState(100);
  const [initialScale, setInitialScale] = useState(100);
  const imageRef = useRef(null);
  const [imageDetailsTask, setImageDetailsTask] = useState(null);
  const [useRandomSeed, setUseRandomSeed] = useState(true);
  const [seed, setSeed] = useState(Math.floor(Math.random() * Number.MAX_SAFE_INTEGER));
  const [useDefaultSize, setUseDefaultSize] = useState(true);
  const [imageWidth, setImageWidth] = useState(1024);
  const [imageHeight, setImageHeight] = useState(1536);
  const [weights, setWeights] = useState({ item1: 0.35, item2: 0.75 }); // 添加强度状态，参考图默认0.35，描述词默认0.75
  const [generationTasks, setGenerationTasks] = useState([]);
  // 添加生成相关状态
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentTaskId, setCurrentTaskId] = useState(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  // 添加TaskContext的使用
  const { updateTask } = useTaskContext();
  
  // 页面刷新/关闭提示
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）';
        return e.returnValue;
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);
  
  // 参考图片上传后更新默认尺寸
  useEffect(() => {
    const updateImageSize = async () => {
      if (referencePanels.length > 0 && referencePanels[0].fileInfo) {
        const panel = referencePanels[0];
        const { width, height } = await getImageSize(panel.url);
        if (width && height) {
          console.log('原始图片尺寸:', width, height);
          
          // 计算最长边
          const maxDimension = Math.max(width, height);
          const scale = maxDimension > 1536 ? 1536 / maxDimension : 1;
          
          // 按比例计算新尺寸
          const newWidth = Math.round(width * scale);
          const newHeight = Math.round(height * scale);
          
          console.log('缩放后尺寸:', newWidth, newHeight);
          setImageWidth(newWidth);
          setImageHeight(newHeight);
        }
      }
    }
    updateImageSize();
  }, [referencePanels]);
  
  // 初始化和清理Masonry布局
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      // 销毁已存在的实例
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }

      // 创建新实例
      masonryRef.current = new Masonry(showcaseRef.current, {
        itemSelector: '.showcase-item',
        columnWidth: '.showcase-item',
        percentPosition: true,
        transitionDuration: '0.3s',
        initLayout: true,
        gutter: 16,
        fitWidth: false,
        horizontalOrder: true
      });

      // 监听窗口大小变化
      const handleResize = () => {
        if (masonryRef.current) {
          setTimeout(() => {
            masonryRef.current.layout();
          }, 100);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (masonryRef.current) {
          masonryRef.current.destroy();
          masonryRef.current = null;
        }
      };
    }
  }, [activeTab]);

  // 监听图片加载
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      const images = showcaseRef.current.getElementsByTagName('img');
      let loadedCount = 0;

      const handleImageLoad = () => {
        loadedCount++;
        if (masonryRef.current) {
          masonryRef.current.layout();
        }
      };

      Array.from(images).forEach(img => {
        if (img.complete) {
          handleImageLoad();
        } else {
          img.addEventListener('load', handleImageLoad);
        }
      });

      return () => {
        Array.from(images).forEach(img => {
          img.removeEventListener('load', handleImageLoad);
        });
      };
    }
  }, [activeTab]);
const handleUploadFileChange = (files) => {
  console.log('handleUploadFileChange',files);
  setReferencePanels(files[0]);
}
  // 处理文件上传
  const handleFileUpload = (file) => {
    setShowUploadGuide(false);
    setUploadGuideFiles([]);
    setUploadGuideGalleryMode(false);
  };

  // 打开上传指导弹窗
  const handleShowUploadGuide = (files, galleryMode) => {
    // 时尚大片页面现在只支持单图上传，先清除之前的参考图片
    if (referencePanels.length > 0) {
      setReferencePanels([]);
    }
    
    // 如果传入多个文件，只取第一个
    if (files && files.length > 1) {
      files = [files[0]];
      message.info('时尚大片页面仅支持上传单张参考图片');
    }
    
    setShowUploadGuide(true);
    setUploadGuideFiles(files || []);
    setUploadGuideGalleryMode(galleryMode);
  };

  const handleUploadResult = (results) => {
    console.log('时装页面处理上传结果:', results);
    setHasUnsavedChanges(true);
    
    if (results.type === 'panels') {
      // 单图上传处理 - 只保留第一个面板
      if (results.panels.length > 0) {
        const panel = results.panels[0];
        // 设置业务类型和来源
        panel.type = 'reference';  // 业务类型：参考图片
        panel.source = 'upload';   // 来源：用户上传
        
        // 保存面板时，保留原始文件对象，用于后续服务器上传
        // 注意：如果结果中包含了file属性，说明它是从本地上传的，需要保留文件对象
        if (panel.file) {
          console.log('本地上传图片，保留文件对象:', panel.file.name);
        }
        
        setReferencePanels([panel]);
      }
    } else if (results.type === 'update') {
      // 处理单个面板更新的情况
      console.log(`更新面板 ${results.panel.componentId} 的状态:`, results.panel);
      
      // 更新参考图片面板，无需抠图处理
      const updatedPanel = {
        ...results.panel,
        status: 'completed',
        originalImage: results.panel.url || results.panel.originalImage,
        error: null
      };
      
      setReferencePanels([updatedPanel]);
    } else if (results.type === 'error') {
      console.error('上传错误:', results.error);
      message.error('上传失败: ' + results.error);
      // 清空参考图片面板
      setReferencePanels([]);
    }
  };

  // 删除参考图组件
  const handleDeleteReferencePanel = (panelId) => {
    setReferencePanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
    setHasUnsavedChanges(true);
  };

  // 重新打开上传弹窗
  const handleReuploadReference = (panel) => {
    if (panel && panel.componentId) {
      handleDeleteReferencePanel(panel.componentId);
      setShowUploadGuide(true);
      setOperationsPanel(null);
      setHasUnsavedChanges(true);
    }
  };

  // 简化handleTextDescriptionSave函数
  const handleTextDescriptionChange = (value) => {
    setTextDescription(value);
    setHasCustomAdvancedPrompt(value.trim() !== '');
    setHasUnsavedChanges(true);
  };

  // 重构生成函数
  const handleGenerate = async () => {
        setSeed(useRandomSeed? (Math.floor(Math.random() * Number.MAX_SAFE_INTEGER)) : seed);

    console.log('开始生成时尚大片...');
    setIsProcessing(true);
    setIsGenerating(true);
    
    try {
      // 检查是否有参考图片上传
      if (referencePanels.length === 0) {
        message.warning('请上传参考图片');
        setIsProcessing(false);
        setIsGenerating(false);
        return;
      }
      
      // 如果有参考图片面板但未处理完成，给出提示
      const hasIncompletePanel = referencePanels.some(panel => panel.status !== 'completed');
      if (hasIncompletePanel) {
        message.warning('请等待参考图片处理完成');
        setIsProcessing(false);
        setIsGenerating(false);
        return;
      }
      
      // 验证描述词是否已填写
      if (!textDescription.trim()) {
        message.error('请在描述词中填写内容');
        setIsProcessing(false);
        setIsGenerating(false);
        return;
      }
      
      // 获取当前用户ID，如果未登录则使用开发者ID
      const userId = getCurrentUserId();
      const balance = await checkUserBalance('时尚大片', 'fashion', imageQuantity);
      if(balance.code !== 200){
        message.error(balance.message);
setIsProcessing(false);
        return;
      }
      // 创建任务ID
      const taskId = generateId(ID_TYPES.TASK);
      setCurrentTaskId(taskId);
      
      // 处理可能的自定义上传图片
      let referenceImageToUse = referencePanels[0];
      
      // 从TaskPanel拖拽过来的参考图片也需要上传到服务器
      if ((referencePanels[0].file && referencePanels[0].source === 'upload') ||
          (referencePanels[0].source === 'upload' && !referencePanels[0].file)) {
        // 显示上传中提示
        message.loading('正在上传参考图片...', 0);
        try {
          let fileToUpload = referencePanels[0].file;
          
          // 如果没有file对象但有URL，需要从URL获取文件
          if (!fileToUpload && referencePanels[0].url) {
            try {
              const response = await fetch(referencePanels[0].url);
              const blob = await response.blob();
              fileToUpload = new File([blob], referencePanels[0].serverFileName || 'reference.jpg', {
                type: blob.type || 'image/jpeg'
              });
            } catch (error) {
              console.error('从URL获取参考图片文件失败:', error);
              message.error('参考图片处理失败，请重试');
              setIsProcessing(false);
              setIsGenerating(false);
              return;
            }
          }
          
          // 将文件上传到服务器
          const {urls, fileInfos} = await uploadFiles([fileToUpload], 'fashion');
          
          // 上传成功后，使用服务器返回的URL更新图片对象
          if (fileInfos) {
            const resultData = fileInfos[0];
            
            // 构建服务器URL
            const serverUrl = resultData.url;
            console.log('参考图片：服务器URL:', serverUrl);
            
            // 创建新的图片对象，包含服务器URL
            referenceImageToUse = {
              ...referencePanels[0],
              url: serverUrl,
              source: 'history', // 标记为已上传到服务器的图片
              originalImage: serverUrl,
              originalUrl: serverUrl,
              serverFileName: referenceImageToUse.serverFileName, // 保存服务器端文件名
              file: undefined, // 清除file属性，避免重复上传和存储不必要的数据
              fileInfo: {
                ...(referencePanels[0].fileInfo || {}),
                serverFileName: resultData.name, // 确保在fileInfo中也设置serverFileName
                ...resultData
              }
            };
            setImageWidth(resultData.width);
            setImageHeight(resultData.height);  
            
            message.success('参考图片上传成功');
          } else {
            message.error('参考图片上传失败');
            setIsProcessing(false);
            setIsGenerating(false);
            return;
          }
        } catch (error) {
          console.error('上传参考图片时出错:', error);
          message.error('参考图片上传失败: ' + (error.message || '未知错误'));
          setIsProcessing(false);
          setIsGenerating(false);
          return;
        } finally {
          // 关闭上传中提示
          message.destroy();
        }
      }
      // 准备任务数据
      const taskData = {
        taskId,
        userId,
        createdAt: new Date().toISOString(),
        status: 'processing',
        imageCount: imageQuantity,
        seed: seed,
        taskType: 'fashion-generation',
        pageType: 'fashion', // 添加页面类型标识
        components: [
          // 参考图组件 - 使用可能已更新的referenceImageToUse
          {
            componentType: 'referencePanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            status: 'completed',
            url: referenceImageToUse.url,
            source: 'history', // 标记为已上传到服务器的图片
            originalImage: referenceImageToUse.originalImage || referenceImageToUse.url,
            name: referenceImageToUse.title || '参考图',
            serverFileName: referenceImageToUse.serverFileName, // 保存服务器端文件名
            fileInfo: {
              ...(referenceImageToUse.fileInfo || {}),
              serverFileName: referenceImageToUse.serverFileName // 确保在fileInfo中也设置serverFileName
            }
          },
          
          // 文字描述组件（如果有）
          ...(textDescription ? [{
            componentType: 'textDescriptionPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '文字描述',
            status: 'completed',
            prompt: textDescription
          }] : []),
          
          // 强度设置组件
          {
            componentType: 'weightPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '强度设置',
            status: 'completed',
            referenceWeight: weights.item1, // 保持0-1范围一致，不再除以10
            descriptionWeight: weights.item2  // 保持0-1范围一致，不再除以10
          },
          
          // 随机种子组件
          {
            componentType: 'randomSeedSelector',
            componentId: generateId(ID_TYPES.COMPONENT),
            useRandom: useRandomSeed,
            value:seed
          },
          
          // 图片尺寸组件（如果指定了自定义尺寸）
          {
            componentType: 'imageSizeSelector',
            componentId: generateId(ID_TYPES.COMPONENT),
            useDefault: false,
            width: imageWidth,
            height: imageHeight
          },
          
          // 数量面板
          {
            componentType: 'quantityPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            quantity: imageQuantity,
            name: "生成数量"
          },
        ],
        
        // 额外添加提示词到任务级属性，确保后端处理时能获取到
        textPrompt: textDescription || '',
        
        // 初始化生成的图片数组，设置为处理中状态
        generatedImages: Array(imageQuantity).fill().map((_, index) => ({
          imageIndex: index,
          status: 'processing',
        })),
        processInfo:{
          results:[]
        }
      };
      
      // 日志输出创建的任务数据，包括提示词信息
      console.log('创建任务数据:', taskData);
      console.log('提示词:', textDescription);
      
      // 使用generationAreaRef的方法添加任务，而不是直接修改本地状态
      if (generationAreaRef.current && generationAreaRef.current.setGenerationTasks) {
        if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      }
      
      // 显示通知
      message.success('已开始生成您的时尚模特展示');
      try {
        // 创建工作流任务
         await createFlowTask(taskData);
      
        // 执行工作流
        const {taskId,promptId,instanceId,status,url} = await executeFlow(WORKFLOW_NAME.FASHION_GENERATION,{
          "4": {
            "noise_seed": seed
          },
          "26": {
            "strength": weights.item1
          },
          //描述词强度调节
          "27": {
            "Number": weights.item2
          },
          //参考图
          "31": {
            "url": referenceImageToUse.url
          },
          //描述词
          "21": {
            "text": textDescription
          },
          "22":{
            "value":imageWidth
          },
          "23":{
            "value":imageHeight
          },
          "25":{
            "batch_size":imageQuantity
          },
          "subInfo":{
            "type": "fashion",
            "title":"时尚大片",
            "count":imageQuantity
          }
        },taskData.taskId);
        setIsProcessing(false);
        setIsGenerating(false);
        setHasUnsavedChanges(false);
        if( generationAreaRef.current){
          taskData.promptId = promptId;
          taskData.instanceId = instanceId;
          taskData.url = url;
          taskData.newTask = true;
          generationAreaRef.current.setGenerationTasks(taskData);
      }
      } catch (error) {
        setIsProcessing(false);
        setIsGenerating(false);
        
        // 更新任务状态为失败
        taskData.status = 'failed';
        taskData.errorMessage = error.message;
        if( generationAreaRef.current){
          generationAreaRef.current.setGenerationTasks(taskData);
        }
        
        // 调用updateTask以触发失败提示音
        updateTask(taskData);
        
        // 更新任务列表
      }
      
    } catch (error) {
      console.error('处理生成请求时出错:', error);
      message.error(`创建任务失败: ${error.message}`);
      setIsProcessing(false);
      setIsGenerating(false);
    }
    setHasUnsavedChanges(false);
  };

  // 处理编辑任务按钮点击
  const handleEditTask = (task) => {
    console.log('编辑任务:', task);
    
    // 设置当前正在编辑的任务ID
    setEditingTaskId(task.taskId);
    
    try {
      // 清空现有的服装面板
      setReferencePanels([]);
      
      // 只处理数组结构，不再兼容对象结构
      const components = Array.isArray(task.components) ? task.components : [];
      console.log('处理的组件数据:', components);
      
      // 获取参考图组件 - 优先查找referencePanel组件
      const referenceComponent = components.find(c => c.componentType === 'referencePanel');
      
      // 如果找到参考图组件，则使用它
      if (referenceComponent) {
        console.log('获取到参考图组件:', referenceComponent);
        // 设置参考图数据 - 始终使用generateId创建新ID
        setReferencePanels([{
          componentId: generateId(ID_TYPES.COMPONENT),
          title: referenceComponent.name || '参考图',
          originalImage: referenceComponent.originalImage || referenceComponent.url,
          url: referenceComponent.originalImage || referenceComponent.url,
          status: 'completed',
          serverFileName: referenceComponent.serverFileName, // 保存服务器文件名
          source: 'history', // 标记为已上传到服务器的图片
          file: undefined // 清除file属性，避免重复上传和存储不必要的数据
        }]);
      } else {
        // 无法找到参考图组件，给出错误提示
        console.warn('未找到参考图组件，无法回填参考图');
        message.warning('无法找到参考图数据');
      }
      
      // 获取文字描述组件
      const textDescriptionComponent = components.find(c => c.componentType === 'textDescriptionPanel');
      
      if (textDescriptionComponent) {
        console.log('获取到文字描述组件:', textDescriptionComponent);
        // 设置文字描述数据 - 只使用prompt属性
        setTextDescription(textDescriptionComponent.prompt || '');
      } else if (task.textPrompt) {
        // 如果组件中没有，但任务级属性中有，也使用它
        console.log('使用任务级提示词:', task.textPrompt);
        setTextDescription(task.textPrompt);
      } else {
        // 清空文字描述
        setTextDescription('');
      }
      
      // 获取强度设置组件 - 处理时尚大片页面的weightPanel组件
      const weightComponent = components.find(c => c.componentType === 'weightPanel');
      
      if (weightComponent) {
        console.log('获取到强度设置组件:', weightComponent);
        
        // 从组件中获取参考图和描述词权重
        const referenceWeight = weightComponent.referenceWeight;
        const descriptionWeight = weightComponent.descriptionWeight;
        
        // 检查是否有有效的权重值
        if (referenceWeight !== undefined && descriptionWeight !== undefined) {
          // 设置权重状态 - 转换为0-1范围的值
          setWeights({
            item1: referenceWeight,
            item2: descriptionWeight
          });
          console.log('回填强度设置:', { item1: referenceWeight, item2: descriptionWeight });
        } else {
          // 使用默认值
          setWeights({ item1: 0.35, item2: 0.75 });
          console.warn('强度设置组件中缺少权重数据，使用默认值');
        }
      } else {
        // 使用默认的均衡权重
        setWeights({ item1: 0.35, item2: 0.75 });
        console.warn('未找到强度设置组件，使用默认均衡权重');
      }
      
      // 获取种子设置 - 只匹配标准组件名称
      const seedComponent = components.find(c => c.componentType === 'randomSeedSelector');
      
      if (seedComponent) {
        console.log('获取到种子设置:', seedComponent);
        // 设置种子数据
        setUseRandomSeed(false);
        setSeed(seedComponent.value);
      } else {
        // 如果没有找到组件，但有顶层seed属性
        if (task.seed !== undefined) {
          console.log('使用顶层seed属性:', task.seed);
          setUseRandomSeed(false);
          setSeed(task.seed);
        } else {
          // 默认使用随机种子
          console.warn('未找到种子组件，使用默认的随机种子');
          setUseRandomSeed(true);
          setSeed(-1);
        }
      }
      
      // 获取尺寸设置 - 只匹配标准组件名称
      const sizeComponent = components.find(c => c.componentType === 'imageSizeSelector');
      
      if (sizeComponent) {
        console.log('获取到尺寸设置:', sizeComponent);
        // 设置尺寸数据
        setUseDefaultSize(sizeComponent.useDefault);
        setImageWidth(sizeComponent.width);
        setImageHeight(sizeComponent.height);
      } else {
        // 查找是否有生成的图片包含尺寸信息
        if (task.generatedImages && task.generatedImages.length > 0 && task.generatedImages[0].fileInfo) {
          const fileInfo = task.generatedImages[0].fileInfo;
          if (fileInfo.width && fileInfo.height) {
            console.log('从生成图片中获取尺寸:', fileInfo);
            setUseDefaultSize(false);
            setImageWidth(fileInfo.width);
            setImageHeight(fileInfo.height);
          } else {
            // 默认使用默认尺寸
            console.warn('未找到尺寸信息，使用默认尺寸');
            setUseDefaultSize(true);
            setImageWidth(1024);
            setImageHeight(1536);
          }
        } else {
          // 默认使用默认尺寸
          console.warn('未找到尺寸组件，使用默认尺寸');
          setUseDefaultSize(true);
          setImageWidth(1024);
          setImageHeight(1536);
        }
      }
      
      // 获取数量设置 - 只匹配标准组件名称
      const quantityComponent = components.find(c => c.componentType === 'quantityPanel');
      
      if (quantityComponent && quantityComponent.quantity) {
        console.log('获取到数量设置:', quantityComponent);
        // 设置图片数量
        setImageQuantity(quantityComponent.quantity);
      } else if (task.imageCount) {
        // 如果没有组件但有顶层imageCount属性，使用它
        setImageQuantity(task.imageCount);
      } else if (task.generatedImages && Array.isArray(task.generatedImages)) {
        // 从生成的图片数量推断
        setImageQuantity(task.generatedImages.length);
      } else {
        // 默认设置为2
        console.warn('未找到数量组件，使用默认数量2');
        setImageQuantity(2);
      }
      
      // 滚动到顶部
      window.scrollTo(0, 0);
      
      // 显示成功消息
      message.success('已加载任务设置');
    } catch (error) {
      console.error('处理编辑任务时出错:', error);
      message.error('加载任务设置失败');
    }
  };

  // 添加点击外部关闭下拉菜单的处理
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdowns = document.querySelectorAll('.dropdown-menu');
      dropdowns.forEach(dropdown => {
        if (!dropdown.parentElement.contains(event.target)) {
          dropdown.classList.remove('show');
        }
      });
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  // 处理查看详情按钮点击
  const handleViewDetails = (image, task, imageIndex) => {
    // 检查是否传入了图片对象，如果是，则显示图片详情
    if (image && typeof image === 'object' && image.url) {
      console.log('查看图片详情:', image);
      console.log('关联任务:', task);
      
      // 只处理数组结构，不再兼容对象结构
      const components = Array.isArray(task.components) ? task.components : [];
      
      // 获取所需组件数据
      const textDescriptionComponent = components.find(c => c.componentType === 'textDescriptionPanel');
      const seedComponent = components.find(c => c.componentType === 'randomSeedSelector');
      const sizeComponent = components.find(c => c.componentType === 'imageSizeSelector');
      const referenceComponent = components.find(c => c.componentType === 'referencePanel'); // 获取参考图组件
      const weightComponent = components.find(c => c.componentType === 'weightPanel'); // 获取强度设置组件
      
      console.log('查看任务详情 - 获取的组件:', {
        textDescriptionComponent,
        seedComponent,
        sizeComponent,
        referenceComponent, // 添加日志输出参考图组件
        weightComponent // 添加日志输出强度设置组件
      });
      
      // 获取任务级种子值或组件种子值
      const taskSeed = task.seed !== undefined ? task.seed : 
                      (seedComponent?.value !== undefined ? seedComponent.value : 
                      (image.seed || Math.floor(Math.random() * Number.MAX_SAFE_INTEGER)));
      
      // 准备适配后的组件数据 - 使用数组结构
      const adaptedComponents = [
        // 种子选择器组件
        {
          componentType: 'randomSeedSelector',
          componentId: generateId(ID_TYPES.COMPONENT),
          useRandom: seedComponent?.useRandom || false,
          value: taskSeed
        },
        
        // 图片尺寸组件
        {
          componentType: 'imageSizeSelector',
          componentId: generateId(ID_TYPES.COMPONENT),
          useDefault: sizeComponent?.useDefault || true,
          width: sizeComponent?.width || (image.fileInfo?.width || 1024),
          height: sizeComponent?.height || (image.fileInfo?.height || 1536)
        },

        // 文字描述组件
        textDescriptionComponent ? {
          ...textDescriptionComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'textDescriptionPanel',
          name: '文字描述',
          status: 'completed',
        } : {
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'textDescriptionPanel',
          name: '文字描述',
          status: 'completed',
          prompt: ''
        }
      ];
      
      // 添加参考图组件
      if (referenceComponent) {
        adaptedComponents.push({
          ...referenceComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'referencePanel'
        });
      }
      
      // 添加强度设置组件
      if (weightComponent) {
        adaptedComponents.push({
          ...weightComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'weightPanel'
        });
      } else {
        // 如果没有找到强度设置组件，但页面类型是时尚大片，添加默认强度设置
        adaptedComponents.push({
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'weightPanel',
          name: '强度调节',
          status: 'completed',
          referenceWeight: 0.35,
          descriptionWeight: 0.75
        });
      }
      
      return {
        ...task,
        // 确保任务对象也包含适配后的组件数据
        ...{
          ...image,
          taskId: task.taskId,
          createdAt: task.createdAt,
          components: adaptedComponents,
          imageIndex: imageIndex !== undefined ? imageIndex : 0,
          seed: taskSeed
        }
      }
    }
  };

  // 添加关闭弹窗时的处理函数
  const handleCloseImageDetails = () => {
    // 直接关闭弹窗，不使用动画
    setShowImageDetails(false);
    
    // 重置状态，无需延迟
    setSelectedImage(null);
    // 重置任务信息，避免保留旧任务导致重新打开时出错
    setImageDetailsTask(null);
    setImagePosition({ x: 0, y: 0 });
    setIsDragging(false);
    lastPosition.current = { x: 0, y: 0 };
    setImageScale(100);
    setInitialScale(100);
    

    
    // 重置文本弹窗状态
    setShowAdvancedText(false);
  };

  // 添加点击外部关闭弹出层的处理函数
  useEffect(() => {
    const handleClickOutside = (event) => {
      // 检查点击是否发生在文本按钮上
      const isTextButton = event.target.closest('.text-button');
      // 检查点击是否发生在弹窗内部
      const isInsidePopup = event.target.closest('.text-popup');
      
      // 如果点击既不是文本按钮也不是弹窗内部，则关闭所有弹窗
      if (!isTextButton && !isInsidePopup) {
        setShowAdvancedText(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 计算初始缩放比例
  const calculateInitialScale = (img) => {
    if (!img) return 100;
    const container = img.parentElement;
    if (!container) return 100;
    
    // 计算图片在容器中的实际显示尺寸与真实尺寸的比例
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const imageNaturalWidth = img.naturalWidth;
    const imageNaturalHeight = img.naturalHeight;
    
    // 计算图片适应容器时的尺寸
    const containerRatio = containerWidth / containerHeight;
    const imageRatio = imageNaturalWidth / imageNaturalHeight;
    
    let scale;
    if (imageRatio > containerRatio) {
      // 图片较宽，以容器宽度为基准
      scale = (containerWidth / imageNaturalWidth) * 100;
    } else {
      // 图片较高，以容器高度为基准
      scale = (containerHeight / imageNaturalHeight) * 100;
    }

    // 如果计算出的缩放比例大于100%，说明图片实际尺寸小于容器
    // 这种情况下我们应该将图片显示为其实际大小
    if (scale > 100) {
      scale = 100;
    }
    
    // 返回相对于真实尺寸的缩放百分比，四舍五入到整数
    return Math.round(scale);
  };

  // 处理图片加载完成
  const handleImageLoad = (e) => {
    const img = e.target;
    const initialScaleValue = calculateInitialScale(img);
    setInitialScale(initialScaleValue);  // 设置初始比例
    setImageScale(initialScaleValue);    // 设置当前比例
    imageRef.current = img;
  };

  // 处理缩放变化
  const handleScaleChange = (newScale) => {
    setImageScale(newScale);
  };

  // 添加复位处理函数
  const handleReset = () => {
    setImageScale(initialScale);
    setImagePosition({ x: 0, y: 0 });
    lastPosition.current = { x: 0, y: 0 };
  };

  const handleReferenceStatusChange = (panelId, newStatus) => {
    setReferencePanels(prevPanels =>
      prevPanels.map(panel =>
        panel.componentId === panelId ? { ...panel, status: newStatus } : panel
      )
    );
  };

  // 添加handleReferencePanelExpandClick函数
  const handleReferencePanelExpandClick = (panel, position) => {
    setOperationsPanel({ panel, position });
  };

  // 权重调整
  const handleWeightChange = (val) => {
    setWeights(val);
    setHasUnsavedChanges(true);
  };

  // 数量调整
  const handleImageQuantityChange = (val) => {
    setImageQuantity(val);
    setHasUnsavedChanges(true);
  };

  // 尺寸调整


  // 使用RequireLogin组件包装整个页面内容
  return (
    <>
      <PromptIfUnsaved when={hasUnsavedChanges} message="操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）" />
      <RequireLogin isLoggedIn={isLoggedIn} featureName="时尚大片功能">
        <div className="fashion-page">
          <div className="fashion-container" ref={containerRef}>
            <ControlPanel
              ref={controlPanelRef}
              width={`${controlPanelWidth}%`}
              onGenerate={handleGenerate}
              disabled={isGenerating}
              featureName="fashion"
              quantity={imageQuantity}
            >
              <div className="upload-area">
                {referencePanels.length === 0 ? (
                  <UploadBox
                    id="reference-upload-box"
                    onChange={handleUploadFileChange}
                    onUpload={handleFileUpload}
                    onShowGuide={handleShowUploadGuide}
                    onUploadResult={handleUploadResult}
                    panels={referencePanels}
                    pageType="fashion"
                    uploadType="reference"
                    showSupportTag={false}
                  />
                ) : null}
                
                {/* 参考图片面板区域 */}
                <div className="reference-panels">
                  {referencePanels.map((panel) => (
                    <ReferencePanel
                      key={panel.componentId}
                      panel={panel}
                      onDelete={handleDeleteReferencePanel}
                      onReupload={handleReuploadReference}
                      onExpandClick={handleReferencePanelExpandClick}
                      isActive={operationsPanel?.panel?.componentId === panel.componentId}
                    />
                  ))}
                </div>
              </div>

              <TextDescriptionPanel
                description={textDescription}
                onChange={handleTextDescriptionChange}
                placeholder="请用自然语言详细描述想要的内容与效果..."
              />

              {/* 添加强度调节组件 */}
              <WeightPanel
                weights={weights}
                onChange={handleWeightChange}
                pageType="fashion"
                item1Label="参考图"
                item2Label="描述词"
                hideItem1={true}
              />

              <RandomSeedSelector
                onRandomChange={setUseRandomSeed}
                onSeedChange={setSeed}
                defaultRandom={useRandomSeed}
                defaultSeed={seed}
                // 编辑模式下传递历史种子
                isEdit={editingTaskId !== null}
                editSeed={generationTasks.find(task => task.taskId === editingTaskId)?.seed || null}
              />

              <ImageSizeSelector
                onUseDefaultChange={setUseDefaultSize}
                onWidthChange={setImageWidth}
                onHeightChange={setImageHeight}
                defaultUseDefault={useDefaultSize}
                defaultWidth={imageWidth}
                defaultHeight={imageHeight}
                pageType="fashion"
                imageData={{
                  uploadedImages: referencePanels
                }}
              />

              <QuantityPanel
                imageQuantity={imageQuantity}
                onChange={handleImageQuantityChange}
              />
            </ControlPanel>

            <ResizeHandle
              ref={handleRef}
              containerRef={containerRef}
              onResize={setControlPanelWidth}
              minWidth={25}
              maxWidth={50}
            />

                    <GenerationArea
              ref={generationAreaRef}    setIsProcessing={setIsGenerating}

              activeTab={activeTab}
              onTabChange={setActiveTab}
              tasks={generationTasks}
              onEditTask={handleEditTask}
              onViewDetails={handleViewDetails}
              pageType="fashion"
            />
          </div>

          {/* 上传指导弹窗 */}
          {showUploadGuide && (
            <UploadGuideModal
              type="reference"
              pageType="fashion"
              initialFiles={uploadGuideFiles}
              initialView={uploadGuideGalleryMode ? 'gallery' : 'guide'}
              onClose={() => setShowUploadGuide(false)}
              onUpload={(result) => {
                console.log('收到上传结果:', result);
                handleUploadResult(result);
                
                // 根据结果中的shouldClose字段决定是否关闭弹窗
                if (result.shouldClose !== false) {
                  setShowUploadGuide(false);
                }
              }}
            />
          )}

          {/* 添加操作弹窗 */}
          {operationsPanel && (
            <ImageInfoModal
              panel={operationsPanel.panel}
              position={operationsPanel.position}
              onClose={() => setOperationsPanel(null)}
              onDelete={handleDeleteReferencePanel}
              onReupload={handleReuploadReference}
              pageType="fashion"
            />
          )}

         

          {/* 添加TaskDetailsModal组件 */}
          {showTaskDetails && taskDetailsData && (
            <Modal
              title={`任务详情 - ${taskDetailsData.id}`}
              open={showTaskDetails}
              onCancel={() => setShowTaskDetails(false)}
              footer={null}
              width={800}
              className="task-details-modal"
            >
              <div className="task-details-content">
                <div className="task-info">
                  <p>创建时间: {new Date(taskDetailsData.createdAt).toLocaleString()}</p>
                  <p>状态: {taskDetailsData.status === 'completed' ? '已完成' : '处理中'}</p>
                </div>
                
                <div className="task-components">
                  {taskDetailsData.components.map((component, index) => (
                    <div key={index} className="component-item">
                      <h4>{component.title}</h4>
                      {component.image && (
                        <div className="component-image">
                          <img src={component.image} alt={component.title} />
                        </div>
                      )}
                      {component.text && (
                        <p>{component.text}</p>
                      )}
                    </div>
                  ))}
                </div>
                
                {taskDetailsData.generatedImages && taskDetailsData.generatedImages.length > 0 && (
                  <div className="generated-images">
                    <h3>生成的图片</h3>
                    <div className="image-grid">
                      {taskDetailsData.generatedImages.map((image, index) => (
                        <div key={index} className="image-item">
                          <img src={image.url} alt={`生成图片 ${index + 1}`} />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </Modal>
          )}


        </div>
      </RequireLogin>
    </>
  );
};

export default FashionPage; 
