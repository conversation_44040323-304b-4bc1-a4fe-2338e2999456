import { createFlowTask, updateFlowTask, getFlowTasks, getFlowTaskDetail, deleteFlowTask,checkUserBalance } from '../../../api/flowtask';
import { uploadFiles } from '../../../api/ossUpload';
import { executeFlow } from '../../../api/flow';
import { WORKFLOW_NAME } from '../../../data/workflowName';import React, { useRef, useEffect, useState, useCallback, Suspense, memo } from 'react';
import './index.css';
import { MdOutlineZoomOutMap, MdOutlineAutoAwesome, MdClose, MdOutlineDescription } from 'react-icons/md';
import Masonry from 'masonry-layout';
import { Modal, Button, Spin, message, Empty, Tooltip } from 'antd';
import 'antd/dist/reset.css';
import { generateId, ID_TYPES } from '../../../utils/idGenerator';
import UploadGuideModal from '../../../components/UploadGuideModal';
import ImageInfoModal from '../../../components/ImageInfoModal';
import ImageDetailsModal from '../../../components/ImageDetailsModal';
import UploadBox from '../../../components/UploadBox';
import ForegroundPanel from '../../../components/ForegroundPanel';
import QuantityPanel from '../../../components/QuantityPanel';
import ControlPanel from '../../../components/ControlPanel';
import ResizeHandle from '../../../components/ResizeHandle';
import PromptIfUnsaved from '../../../components/PromptIfUnsaved';
import GenerationArea from '../../../components/GenerationArea';
import ScenePanel from '../../../components/ScenePanel';
import SceneSelectModal from '../../../components/SceneSelectModal';
import RequireLogin from '../../../components/RequireLogin';
import request from '../../../api/request';
import ImageZoomControl from '../../../components/ImageZoomControl';
import ImageNavigator from '../../../components/ImageNavigator';
import ThumbnailList from '../../../components/ThumbnailList';
import { getCurrentUserId } from '../../../api';

import { showDeleteConfirmModal } from '../../../utils/modalUtils';
import JSZip from 'jszip';
import RandomSeedSelector from '../../../components/RandomSeedSelector';
import { getFakeTasksForUser } from '../../../api/task';
import { handleBatchDownload as downloadHelper } from '../../../utils/downloadHelper';
import { getTaskComponent } from '../../../utils/taskAdapters';
import { uploadImage } from '../../../api/upload';
import { useTaskContext } from '../../../contexts/TaskContext';

const MemoizedImageDetailsModal = React.memo(ImageDetailsModal);

const BackgroundPage = ({ isLoggedIn, userId }) => {
  const containerRef = useRef(null);
  const controlPanelRef = useRef(null);
  const handleRef = useRef(null);
  const [activeTab, setActiveTab] = useState('result');
  const [controlPanelWidth, setControlPanelWidth] = useState(28);
  const showcaseRef = useRef(null);
  const masonryRef = useRef(null);
  const [showUploadGuide, setShowUploadGuide] = useState(false);
  const [uploadGuideType, setUploadGuideType] = useState('foreground');
  const [isProcessing, setIsProcessing] = useState(false);
  const [foregroundPanels, setForegroundPanels] = useState([]);
  const [currentReuploadForegroundPanelId, setCurrentReuploadForegroundPanelId] = useState(null);
  const [showForegroundUploadGuide, setShowForegroundUploadGuide] = useState(false);
  const [operationsPanel, setOperationsPanel] = useState(null);
  const [showImageInfo, setShowImageInfo] = useState(false);
  const [currentImagePanel, setCurrentImagePanel] = useState(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [imageQuantity, setImageQuantity] = useState(2);
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageDetails, setShowImageDetails] = useState(false);
  const [imageDetailsTask, setImageDetailsTask] = useState(null);
  const [showAdvancedText, setShowAdvancedText] = useState(false);

  const [advancedPopupPosition, setAdvancedPopupPosition] = useState({ top: 0, left: 0 });

  // 添加拖动状态管理
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const lastPosition = useRef({ x: 0, y: 0 });
  const generationAreaRef=useRef(null);

  // 添加图片缩放相关状态
  const [imageScale, setImageScale] = useState(100);
  const [initialScale, setInitialScale] = useState(100);
  const imageRef = useRef(null);

  // 添加随机种子相关状态
  const [useRandomSeed, setUseRandomSeed] = useState(true);
  const [seed, setSeed] = useState(Math.floor(Math.random() * Number.MAX_SAFE_INTEGER));

  // 添加加载状态
  const [isLoadingTasks, setIsLoadingTasks] = useState(false);

  // 任务列表状态
  const [generationTasks, setGenerationTasks] = useState([]);

  // 添加场景选择相关的状态
  const [showSceneSelect, setShowSceneSelect] = useState(false);
  const [selectedScene, setSelectedScene] = useState(null);
  const [customSceneSettings, setCustomSceneSettings] = useState(null);
  const [hasCustomScenePrompt, setHasCustomScenePrompt] = useState(false);

  // 添加TaskContext的使用
  const { updateTask } = useTaskContext();

  // 页面刷新/关闭提示
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）';
        return e.returnValue;
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);

  // 初始化和清理Masonry布局
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      // 销毁已存在的实例
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }

      // 创建新实例
      masonryRef.current = new Masonry(showcaseRef.current, {
        itemSelector: '.showcase-item',
        columnWidth: '.showcase-item',
        percentPosition: true,
        transitionDuration: '0.3s',
        initLayout: true,
        gutter: 16,
        fitWidth: false,
        horizontalOrder: true
      });

      // 监听窗口大小变化
      const handleResize = () => {
        if (masonryRef.current) {
          setTimeout(() => {
            masonryRef.current.layout();
          }, 100);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (masonryRef.current) {
          masonryRef.current.destroy();
          masonryRef.current = null;
        }
      };
    }
  }, [activeTab]);

  // 监听图片加载
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      const images = showcaseRef.current.getElementsByTagName('img');
      let loadedCount = 0;

      const handleImageLoad = () => {
        loadedCount++;
        if (masonryRef.current) {
          masonryRef.current.layout();
        }
      };

      Array.from(images).forEach(img => {
        if (img.complete) {
          handleImageLoad();
        } else {
          img.addEventListener('load', handleImageLoad);
        }
      });

      return () => {
        Array.from(images).forEach(img => {
          img.removeEventListener('load', handleImageLoad);
        });
      };
    }
  }, [activeTab]);

  // 处理删除前景图面板
  const handleDeleteForegroundPanel = (panelId) => {
    setForegroundPanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
    setHasUnsavedChanges(true);
  };

  // 处理重新上传前景图
  const handleReuploadForeground = (panel) => {
    if (panel && panel.componentId) {
      // 记录正在重新上传的面板ID
      setCurrentReuploadForegroundPanelId(panel.componentId);
      // 打开上传指南
      setShowForegroundUploadGuide(true);
    }
  };

  // 处理前景图上传结果
  const handleForegroundUploadResult = (results) => {
    setHasUnsavedChanges(true);
    console.log('处理前景图上传结果:', results);
    
    if (results.type === 'panels') {
      const panelsWithType = results.panels.map(panel => ({
        ...panel,
        type: 'foreground',
        title: panel.title || panel.name || '前景图', // 确保设置title属性
        // 始终使用generateId生成新ID
        componentId: generateId(ID_TYPES.COMPONENT),
        // 确保设置source属性，特别是从TaskPanel拖拽过来的图片
        source: panel.source || (panel.file ? 'upload' : 'history')
      }));
      
      if (currentReuploadForegroundPanelId) {
        // 如果是重新上传，替换原有面板
        setForegroundPanels(prevPanels => 
          prevPanels.map(panel => 
            panel.componentId === currentReuploadForegroundPanelId 
              ? { ...panelsWithType[0], componentId: currentReuploadForegroundPanelId, type: 'foreground' }
              : panel
          )
        );
        // 重置当前重新上传的面板ID
        setCurrentReuploadForegroundPanelId(null);
      } else {
        // 如果是新上传，添加新面板
        setForegroundPanels(prevPanels => [...prevPanels, ...panelsWithType]);
      }
    } else if (results.type === 'update') {
      // 处理状态更新的情况
      console.log(`更新面板 ${results.panelId} 的状态:`, results.data);
      
      setForegroundPanels(prevPanels => 
        prevPanels.map(panel => {
          if (panel.componentId === results.panelId) {
            // 根据processed数据更新面板
            if (results.data?.processed) {
              const processedData = results.data.processed;
              
              // 构建处理后图片的URL
              let processedImageUrl = null;
              if (processedData.url) {
                processedImageUrl = processedData.url;
              } else if (processedData.relativePath) {
                processedImageUrl = `http://localhost:3002${processedData.relativePath}`;
              } else if (processedData.processedFile) {
                processedImageUrl = `http://localhost:3002/rmbged/${processedData.processedFile}`;
              }
              console.log('processedImageUrl',{
                ...panel,
                status: 'completed',  // 更新状态为已完成
                originalImage: processedData.originalUrl || panel.previewUrl || panel.url, // 保存原始图片URL
                processedFile: processedImageUrl,
                fileInfo: processedData.fileInfo || panel.fileInfo,
                error: null,
                title: panel.title || panel.name || '前景图' // 确保title属性存在
              })
              
              return {
                ...panel,
                status: 'completed',  // 更新状态为已完成
                originalImage: processedData.originalUrl || panel.previewUrl || panel.url, // 保存原始图片URL
                processedFile: processedImageUrl,
                fileInfo: processedData.fileInfo || panel.fileInfo,
                error: null,
                title: panel.title || panel.name || '前景图' // 确保title属性存在
              };
            }
            
            // 如果没有processed数据，检查是否有错误
            if (results.error) {
              return {
                ...panel,
                status: 'error',
                error: results.error
              };
            }
            
            // 如果既没有processed数据也没有错误，保持原样
            return panel;
          }
          
          // 不是要更新的面板，保持不变
          return panel;
        })
      );
    } else if (results.type === 'error') {
      console.error('上传错误:', results.error);
      message.error('上传失败: ' + results.error);
      // 移除处理中的面板
      setForegroundPanels(prevPanels => 
        prevPanels.filter(panel => panel.status !== 'processing'));
      // 重置当前重新上传的面板ID
      setCurrentReuploadForegroundPanelId(null);
    }
  };

  // 处理前景图状态变化
  const handleForegroundStatusChange = (panelId, newStatus) => {
    setForegroundPanels(prevPanels =>
      prevPanels.map(panel =>
        panel.componentId === panelId ? { ...panel, status: newStatus } : panel
      )
    );
  };

  // 处理开始生成按钮点击
  const handleGenerate = async () => {
    setIsProcessing(true);
    console.log('开始生成...');
    let taskData;
    setSeed(useRandomSeed? (Math.floor(Math.random() * Number.MAX_SAFE_INTEGER)) : seed);

    try {
      // 检查是否有前景图面板
      if (foregroundPanels.length === 0) {
        message.error('请先上传前景图片');
        setIsProcessing(false);
        return;
      }

      // 检查是否选择了场景
      if (!selectedScene) {
        message.error('请选择背景');
        setIsProcessing(false);
        return;
      }
      // 获取当前用户ID
      const currentUserId = userId || getCurrentUserId() || 'developer';
      const balance = await checkUserBalance('换背景', 'background', imageQuantity);
      if(balance.code !== 200){
        message.error(balance.message);
      setIsProcessing(false);
        return;
      }
      // 处理可能的自定义上传的场景图片
      let sceneToUse = selectedScene;

      // 检查是否有自定义上传的场景参考图片（同时检查文件对象和类型）
      if (selectedScene.file) {
        // 显示上传中提示
        message.loading('正在上传场景参考图片...', 0);

        try {
          // 将文件上传到服务器
            // 上传版型图片到服务器
            const uploadResult = await uploadFiles([selectedScene.file],"trending");
          if (uploadResult) {
            const resultData = uploadResult.fileInfos[0];
            sceneToUse = {
              ...selectedScene,
              image: resultData.url, // 使用构建的服务器URL
              url: resultData.url, // 同时设置url属性以保持一致性
              serverFileName: resultData.serverFileName,
              source: 'history'
            };
            message.success('场景参考图片上传成功');
          } else {
            message.error('场景参考图片上传失败');
            setIsProcessing(false);
            return;
          }
        } catch (error) {
          console.error('上传场景参考图片时出错:', error);
          message.error('场景参考图片上传失败: ' + (error.message || '未知错误'));
          setIsProcessing(false);
          return;
        } finally {
          // 关闭上传中提示
          message.destroy();
        }
      }

      // 处理可能的本地上传的前景图片
      let foregroundToUse = foregroundPanels[0];

      // 检查是否有自定义上传的前景图片（同时检查文件对象和类型）
      // 从TaskPanel拖拽过来的图片也需要上传到服务器
      if ((foregroundPanels[0].file && foregroundPanels[0].source === 'upload') ||
          (foregroundPanels[0].source === 'upload' && !foregroundPanels[0].file)) {
        // 显示上传中提示
        message.loading('正在上传前景图片...', 0);

        try {
          let fileToUpload = foregroundPanels[0].file;
          
          // 如果没有file对象但有URL，需要从URL获取文件
          if (!fileToUpload && foregroundPanels[0].url) {
            try {
              const response = await fetch(foregroundPanels[0].url);
              const blob = await response.blob();
              fileToUpload = new File([blob], foregroundPanels[0].serverFileName || 'image.jpg', {
                type: blob.type || 'image/jpeg'
              });
            } catch (error) {
              console.error('从URL获取文件失败:', error);
              message.error('图片处理失败，请重试');
              setIsProcessing(false);
              return;
            }
          }
          
          const uploadResult = await uploadFiles([fileToUpload],"trending");
          if (uploadResult) {
           const resultData = uploadResult.fileInfos[0];

          // 上传成功后，使用服务器返回的URL更新前景图对象
            foregroundToUse = {
              ...foregroundPanels[0],
              url: resultData.url,
              image: resultData.url, // 确保image字段与url保持一致
              serverFileName: foregroundPanels[0].serverFileName,
              source: 'history',
              file: undefined, // 清除file属性，避免重复上传和存储不必要的数据
              fileInfo: {
                ...(foregroundPanels[0].fileInfo || {}),
                serverFileName: resultData.serverFileName
              }
            };

            message.success('前景图片上传成功');
          } else {
            message.error('前景图片上传失败');
            setIsProcessing(false);
            return;
          }
        } catch (error) {
          console.error('上传前景图片时出错:', error);
          message.error('前景图片上传失败: ' + (error.message || '未知错误'));
          setIsProcessing(false);
          return;
        } finally {
          // 关闭上传中提示
          message.destroy();
        }
      }

      // 创建一个新的任务ID
      const taskId = generateId(ID_TYPES.TASK);

      // 构建任务对象 - 只使用数组结构
      const taskData = {
        taskId: taskId,
        pageType: 'background',
        userId: currentUserId,
        createdAt: new Date(),
        status: 'processing',
        imageCount: imageQuantity,
        taskType: 'background', // 指定任务类型为背景替换
        // 使用数组结构，始终使用generateId创建新ID
        components: [
          {
            componentType: 'foregroundPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: foregroundToUse.title || '前景图',
            title: foregroundToUse.title || '前景图',
            status: 'completed',
            serverFileName: foregroundToUse.serverFileName,
            originalImage: foregroundToUse.url,
            processedFile: foregroundToUse.processedFile,
            fileInfo: {
              ...(foregroundToUse.fileInfo || {}),
              serverFileName: foregroundToUse.serverFileName
            },
            url: foregroundToUse.url
          },
          {
            componentType: 'scenePanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: sceneToUse.name || '背景场景',
            sceneReferenceId: sceneToUse.id, // 场景参考ID，用于标识具体的场景图片（如：beach-02, pool-01等）
            type: sceneToUse.type || (sceneToUse.id ? 'preset' : 'custom'), // 场景类型：preset(预设场景) 或 custom(自定义场景)
            image: sceneToUse.image || sceneToUse.thumbnail, // 场景图片URL，用于预览和AI处理
            serverFileName: sceneToUse.serverFileName, // 服务器存储的文件名（仅自定义上传场景）
            prompt: sceneToUse.prompt, // 自定义场景的正面提示词
            negative_prompt: sceneToUse.negative_prompt // 自定义场景的负面提示词
          },
          // 随机种子组件
          {
            componentType: 'randomSeedSelector',
            componentId: generateId(ID_TYPES.COMPONENT),
            useRandom: false,
            value:seed
          },
          // 数量组件
          {
            componentType: 'quantityPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            quantity: imageQuantity
          }
        ],
        // 简化的settings结构
        settings: {
          common: {
            imageQuantity: imageQuantity
          },
          pageSpecific: {
            generation: {
              count: imageQuantity,
              prompt: sceneToUse ?
                (sceneToUse.type === 'custom' ?
                  sceneToUse.prompt :
                  `将图片背景替换为${sceneToUse.name}场景`)
                : '将图片背景替换为干净的纯色背景'
            }
          }
        },
        // 使用新的generatedImages数组
        generatedImages: Array(imageQuantity).fill(null).map((_, index) => ({
          imageIndex: index,
          status: 'processing'
        })),
        processInfo:{
          results:[]
        }
      };


    // 先添加到本地状态，使UI立即响应
      if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      await createFlowTask(taskData);
      const {promptId,instanceId,status,url} = await executeFlow(WORKFLOW_NAME.BACKGROUND,
        {
          "36": {
        "amount": imageQuantity
            },
          "10":{
            "seed":seed
          },
          "38": {
            "url":sceneToUse.image
            },
          "39": {
            "url":foregroundToUse.url
          },
          "subInfo":{
            "type": "background",
            "title":"换背景",
            "count":imageQuantity
          }
        },taskId);
        setIsProcessing(false);
        setHasUnsavedChanges(false); 
        if( generationAreaRef.current){
          taskData.promptId = promptId;
          taskData.instanceId = instanceId;
          taskData.url = url;
          taskData.newTask = true;
          generationAreaRef.current.setGenerationTasks(taskData);
      }
    } catch (error) {
      console.error('创建任务失败:', error);
      message.error('创建任务失败: ' + error.message);
      taskData.status = 'failed';

      if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      
      // 调用updateTask以触发失败提示音
      updateTask(taskData);
    }
    
  };

  // 处理单张图片下载
  const handleDownloadImage = async (imageUrl, taskId, index) => {
    try {
      message.info('准备下载...');
      const httpsUrl = imageUrl.replace(/^http:/, 'https:');
      const response = await fetch(httpsUrl);
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `${taskId}_${parseInt(index) + 1}.jpg`;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl);
    } catch (error) {
      message.error('下载失败');
      console.error('下载出错:', error);
    }
  };
  // 处理编辑任务
  const handleEditTask = (task) => {
    if (!task) return;

    try {
      // 控制台记录任务数据
      console.log('回填任务数据:', task);

      // 只处理任务组件为数组的情况
      const components = Array.isArray(task.components) ? task.components : [];
      console.log('处理的组件数据:', components);

      // 获取组件数据
      const foregroundComponent = components.find(c => c.componentType === 'foregroundPanel');
      const sceneComponent = components.find(c => c.componentType === 'scenePanel');
      const quantityComponent = components.find(c => c.componentType === 'quantityPanel');
      const seedComponent = components.find(c => c.componentType === 'randomSeedSelector');

      // 构建初始状态
      if (foregroundComponent) {
        console.log('获取到前景图组件:', foregroundComponent);
        // 获取前景图信息
        let fileInfo = foregroundComponent.fileInfo;

        // 过滤掉过大或过小的尺寸值
        if (fileInfo && fileInfo.width && fileInfo.height) {
          let { width, height } = fileInfo;
          if (width > 2048) {
            const ratio = 2048 / width;
            width = 2048;
            height = Math.round(height * ratio);
          }
          if (height > 2048) {
            const ratio = 2048 / height;
            height = 2048;
            width = Math.round(width * ratio);
          }
          fileInfo = { ...fileInfo, width, height };
        }

        // 构建初始化的前景图面板
        const initialForegroundPanel = {
          ...foregroundComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          id: generateId(ID_TYPES.COMPONENT), // 直接生成新ID，不使用兼容方案
          title: foregroundComponent.name || '前景图', // 将name映射到title
          status: 'completed',
          type: 'foreground', // 确保类型正确
          fileInfo: fileInfo,
          // 确保处理过的文件URL
          processedFile: foregroundComponent.processedFile || foregroundComponent.url || foregroundComponent.image
        };

        // 设置面板状态
        setForegroundPanels([initialForegroundPanel]);
      }

      // 如果存在场景设置，则恢复场景设置
      if (sceneComponent) {
        setSelectedScene({
          ...sceneComponent,
          componentId: generateId(ID_TYPES.COMPONENT)
        });
      }

      // 恢复数量设置
      if (quantityComponent) {
        // 注意：quantityPanel可能使用value或quantity字段
        const quantity = quantityComponent.value || quantityComponent.quantity;
        if (quantity) {
          setImageQuantity(quantity);
        }
      } else if (task.imageCount) {
        // 回退到任务顶层属性
        setImageQuantity(task.imageCount);
      }

      // 恢复随机种子设置
      if (seedComponent) {
        // 检查是否有实际使用的种子值
        if (seedComponent.value >= 0 && !seedComponent.useRandom) {
          // 如果存在实际使用的种子值，无论原来是否设置为随机，都回填为固定种子
          setUseRandomSeed(false);
          setSeed(seedComponent.value);
          console.log('回填固定种子值:', seedComponent.value);
        } else {
          // 如果没有固定的种子值，则保持原有设置
          setUseRandomSeed(seedComponent.useRandom);
          if (!seedComponent.useRandom && seedComponent.value !== -1) {
            setSeed(seedComponent.value);
          }
          console.log('回填种子设置:', seedComponent);
        }
      }
      // 检查任务本身是否有种子值
      else if (task.seed !== undefined) {
        // 如果任务有种子值，回填为固定种子
        setUseRandomSeed(false);
        setSeed(task.seed);
        console.log('回填任务种子值:', task.seed);
      }

      // 切换到结果标签页
      setActiveTab('result');

      message.success({
        content: '配置已重新导入，可继续进行调整',
        duration: 5
      });

      console.log('重新编辑任务:', {
        foregroundPanel: foregroundComponent,
        scene: sceneComponent,
        seed: seedComponent || task.seed,
        activeTab: 'result'
      });
    } catch (error) {
      console.error('恢复任务设置失败:', error);
      message.error('恢复任务设置失败: ' + (error.message || '未知错误'));
    }
  };

  // 添加点击外部关闭下拉菜单的处理
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdowns = document.querySelectorAll('.dropdown-menu');
      dropdowns.forEach(dropdown => {
        if (!dropdown.parentElement.contains(event.target)) {
          dropdown.classList.remove('show');
        }
      });
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);
  const handleUploadFileChange = (files) => {
    console.log('前景图上传:', files);
  };
  const handleForegroundFileUpload = (files) => {
    console.log('前景图上传:', files);
  };

  // 查看图片详情函数
  const handleViewDetails = (image, task) => {
    // 只处理task.components为数组的情况
    const components = Array.isArray(task.components) ? task.components : [];
    console.log('查看详情 - 处理的组件数据:', components);

    // 获取所需组件数据
    const foregroundComponent = components.find(c => c.componentType === 'foregroundPanel');
    const seedComponent = components.find(c => c.componentType === 'randomSeedSelector');
    const sceneComponent = components.find(c => c.componentType === 'scenePanel');

    console.log('查看任务详情 - 获取的组件:', {
      foregroundComponent,
      seedComponent,
      sceneComponent
    });

    // ===== 种子值处理逻辑，严格按照规范优先级 =====
    // 1. 优先使用任务级seed
    // 2. 其次使用组件seed值（如果存在且>=0）
    // 3. 最后才使用图片级seed
    const taskSeed = task.seed !== undefined && task.seed >= 0 ? task.seed :
                   (seedComponent?.value !== undefined && seedComponent.value >= 0 ?
                    seedComponent.value : (image.seed || 0));

    // 准备标准化的组件数据 - 使用数组结构，始终使用generateId创建新ID
    const adaptedComponents = [
      {
        componentType: 'randomSeedSelector',
        componentId: generateId(ID_TYPES.COMPONENT),
        useRandom: seedComponent?.useRandom || false,
        // 优先使用任务级seed，其次使用组件seed值
        value: taskSeed
      },
      {
        componentType: 'scenePanel',
        componentId: generateId(ID_TYPES.COMPONENT),
        ...(sceneComponent || {
        selectedScene: {
          id: 'auto',
          name: '自动选择',
          thumbnail: '/images/scenes/auto.jpg',
          prompt: '自动选择'
        }
        })
      }
    ];

    // 添加前景图组件数据
    if (foregroundComponent) {
      adaptedComponents.push({
        ...foregroundComponent,
        componentId: generateId(ID_TYPES.COMPONENT),
        componentType: 'foregroundPanel'
      });
    }
    // 在下一个微任务中设置其他初始状态
    queueMicrotask(() => {
      // 初始化图片位置和缩放状态
      setImagePosition({ x: 0, y: 0 });
      setIsDragging(false);
      lastPosition.current = { x: 0, y: 0 };
      setImageScale(100);
      setInitialScale(100);
    });
    return {
      ...task,
      // 确保任务对象也包含适配后的组件数据，供ImageDetailsModal使用
      adaptedComponents
    }

  };

  // 添加关闭弹窗时的处理函数
  const handleCloseImageDetails = () => {
    // 直接关闭弹窗，不使用动画
    setShowImageDetails(false);

    // 重置状态，无需延迟
    setSelectedImage(null);
    // 重置任务信息，避免保留旧任务导致重新打开时出错
    setImageDetailsTask(null);
    setImagePosition({ x: 0, y: 0 });
    setIsDragging(false);
    lastPosition.current = { x: 0, y: 0 };
    setImageScale(100);
    setInitialScale(100);



    // 重置文本弹窗状态
    setShowAdvancedText(false);
  };

  // 添加点击外部关闭弹出层的处理函数
  useEffect(() => {
    const handleClickOutside = (event) => {
      // 检查点击是否发生在文本按钮上
      const isTextButton = event.target.closest('.text-button');
      // 检查点击是否发生在弹窗内部
      const isInsidePopup = event.target.closest('.text-popup');

      // 如果点击既不是文本按钮也不是弹窗内部，则关闭所有弹窗
      if (!isTextButton && !isInsidePopup) {
        setShowAdvancedText(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 计算初始缩放比例
  const calculateInitialScale = (img) => {
    if (!img) return 100;
    const container = img.parentElement;
    if (!container) return 100;

    // 计算图片在容器中的实际显示尺寸与真实尺寸的比例
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const imageNaturalWidth = img.naturalWidth;
    const imageNaturalHeight = img.naturalHeight;

    // 计算图片适应容器时的尺寸
    const containerRatio = containerWidth / containerHeight;
    const imageRatio = imageNaturalWidth / imageNaturalHeight;

    let scale;
    if (imageRatio > containerRatio) {
      // 图片较宽，以容器宽度为基准
      scale = (containerWidth / imageNaturalWidth) * 100;
    } else {
      // 图片较高，以容器高度为基准
      scale = (containerHeight / imageNaturalHeight) * 100;
    }

    // 如果计算出的缩放比例大于100%，说明图片实际尺寸小于容器
    // 这种情况下我们应该将图片显示为其实际大小
    if (scale > 100) {
      scale = 100;
    }

    // 返回相对于真实尺寸的缩放百分比，四舍五入到整数
    return Math.round(scale);
  };

  // 处理图片加载完成
  const handleImageLoad = (e) => {
    const img = e.target;
    const initialScaleValue = calculateInitialScale(img);
    setInitialScale(initialScaleValue);  // 设置初始比例
    setImageScale(initialScaleValue);    // 设置当前比例
    imageRef.current = img;
  };

  // 处理缩放变化
  const handleScaleChange = (newScale) => {
    setImageScale(newScale);
  };

  // 处理重置图片位置和缩放
  const handleReset = () => {
    setImageScale(initialScale);
    setImagePosition({ x: 0, y: 0 });
    lastPosition.current = { x: 0, y: 0 };
  };

  // 处理场景选择
  const handleSceneSelect = (scene) => {
    // 确保scene.id为字符串类型
    if (scene && typeof scene.id === 'number') {
      scene = { ...scene, id: String(scene.id) };
    }
    setSelectedScene(scene);
    setHasUnsavedChanges(true);
  };

  // 处理自定义场景设置变化
  const handleCustomSceneSettingsChange = (settings) => {
    setCustomSceneSettings(settings);
    setHasUnsavedChanges(true);
  };

  // 创建缩略图并保存到历史记录
  const createThumbnailAndSaveToHistory = (file, localUrl, serverUrl, fileName, imageType, pageType) => {
    try {
      console.log('创建缩略图并保存到历史记录:', fileName);

      // 创建图片对象用于加载文件
      const img = new Image();
      img.onload = () => {
        try {
          // 创建canvas用于绘制缩略图
          const canvas = document.createElement('canvas');

          // 设置缩略图最大尺寸
          const MAX_THUMBNAIL_SIZE = 400;

          // 计算缩放比例
          let width = img.width;
          let height = img.height;
          const aspectRatio = width / height;

          if (width > height) {
            // 横向图片
            if (width > MAX_THUMBNAIL_SIZE) {
              width = MAX_THUMBNAIL_SIZE;
              height = width / aspectRatio;
            }
          } else {
            // 纵向图片
            if (height > MAX_THUMBNAIL_SIZE) {
              height = MAX_THUMBNAIL_SIZE;
              width = height * aspectRatio;
            }
          }

          // 设置canvas尺寸
          canvas.width = width;
          canvas.height = height;

          // 绘制缩放后的图片
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0, width, height);

          // 输出为低质量的JPEG (质量0.6)
          const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.6);

          // 计算缩略图大小（KB）
          const base64Data = thumbnailDataUrl.split(',')[1];
          const byteSize = atob(base64Data).length;
          const kiloByteSize = byteSize / 1024;

          console.log(`缩略图创建成功，原始大小: ${img.width}x${img.height}, 缩略图大小: ${width}x${height}, 文件大小: ${kiloByteSize.toFixed(2)}KB`);

          // 保存到历史记录
          const success = saveToHistory({
            id: Date.now().toString(),
            url: localUrl,
            thumbnailUrl: thumbnailDataUrl,
            serverUrl: serverUrl,
            fileName: fileName,
            type: imageType,
            pageType: pageType,
            fileType: file.type,
            saveTime: Date.now()
          });

          if (success) {
            console.log(`历史记录保存成功, 文件名: ${fileName}`);
          } else {
            console.warn(`历史记录保存失败, 文件名: ${fileName}`);
          }
        } catch (error) {
          console.error('创建缩略图过程中出错:', error);
        }
      };

      img.onerror = (error) => {
        console.error('加载图片出错:', error);
      };

      // 使用本地URL加载图片
      img.src = localUrl;
    } catch (error) {
      console.error('处理图片文件时出错:', error);
    }
  };

  // 保存到历史记录，返回布尔值而非Promise
  const saveToHistory = (record) => {
    // 使用更明确的类型定义方式，避免不一致
    if (!record || !record.fileName || !record.serverUrl || typeof record.serverUrl !== 'string' || record.serverUrl.trim() === '') {
      console.warn('记录无效或没有服务器URL，无法保存到历史记录');
      return false;
    }

    try {
      const MAX_HISTORY_COUNT = 10;
      let history = [];
      // 使用更明确的类型定义方式，避免不一致
      const imageType = record.type || 'foreground';
      const pageType = record.pageType || 'background';
      const historyKey = `upload_history_${imageType}_${pageType}`;
      console.log('保存历史记录，使用键值:', historyKey);

      try {
        const historyJson = localStorage.getItem(historyKey);
        if (historyJson) {
          history = JSON.parse(historyJson);
          if (!Array.isArray(history)) {
            console.warn('历史记录格式无效，重置为空数组');
            history = [];
          }
        }
      } catch (e) {
        console.warn('解析历史记录JSON失败，重置为空数组', e);
        history = [];
      }

      // 检查是否已存在相同服务器URL的记录
      const existingIndex = history.findIndex(item =>
        item.serverUrl === record.serverUrl &&
        (item.type === record.type || (!item.type && !record.type)) &&
        (item.pageType === record.pageType || (!item.pageType && !record.pageType))
      );

      if (existingIndex !== -1) {
        console.log(`找到已存在的记录 [${existingIndex}]，服务器URL: ${record.serverUrl}，更新而不是添加新记录`);

        // 保留原始ID，更新其他信息
        record.id = history[existingIndex].id;

        // 从历史记录中移除旧记录
        history.splice(existingIndex, 1);
      } else {
        console.log(`未找到相同服务器URL的记录，添加新记录: ${record.serverUrl}`);

        // 如果记录已经达到最大数量，移除最早的记录
        if (history.length >= MAX_HISTORY_COUNT) {
          const removed = history.pop();
          console.log(`历史记录达到最大限制 ${MAX_HISTORY_COUNT}，移除最旧记录: ${removed?.fileName || '未知'}`);
        }
      }

      // 删除文件对象，避免序列化错误
      const recordToSave = { ...record };
      delete recordToSave.file;

      // 将记录添加到数组开头（最新的在前面）
      history.unshift(recordToSave);

      // 保存回localStorage
      localStorage.setItem(historyKey, JSON.stringify(history));
      console.log(`成功保存历史记录，当前共有 ${history.length} 条记录`);

      // 验证保存是否成功
      const verifyRecords = localStorage.getItem(historyKey);
      if (verifyRecords) {
        const parsed = JSON.parse(verifyRecords);
        console.log(`验证保存：记录已成功保存，当前共有${parsed.length}条记录`);
      }

      return true;
    } catch (err) {
      console.error('保存到历史记录出错:', err);
      return false;
    }
  };

  return (
    <RequireLogin isLoggedIn={isLoggedIn} featureName="换背景功能">
      <PromptIfUnsaved when={hasUnsavedChanges} message="操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）" />
      <div className="background-page">
        <div className="background-container" ref={containerRef}>
          <ControlPanel
            ref={controlPanelRef}
            width={`${controlPanelWidth}%`}
            onGenerate={handleGenerate}
            disabled={isProcessing}
            featureName="background"
            quantity={imageQuantity}
          >
    {/* 前景图上传区域或前景图面板 - 位于最上方 */}
    {foregroundPanels.length === 0 ? (
                      <UploadBox
                        id="foreground-upload-box"
                        onUpload={handleForegroundFileUpload}
                        onShowGuide={() => {
                          setUploadGuideType('foreground');
                          setShowForegroundUploadGuide(true);
                        }}
                        onUploadResult={handleForegroundUploadResult}
                        panels={foregroundPanels}
                        className="mt-2"
                        showSupportTag={false}
                        pageType="background"
                        uploadType="foreground"
                      />
                    ) : (
                      // 展示前景图面板
                      foregroundPanels.map((panel) => (
                        <ForegroundPanel
                          key={panel.componentId}
                          panel={panel}
                          onExpandClick={(panel, position) => {
                            setOperationsPanel({
                              panel: panel,
                              position
                            });
                          }}
                          onDelete={() => handleDeleteForegroundPanel(panel.componentId)}
                          onReupload={() => handleReuploadForeground(panel)}
                          onStatusChange={(newStatus) => handleForegroundStatusChange(panel.componentId, newStatus)}
                          isActive={panel.status === 'completed'}
                          onPanelsChange={setForegroundPanels}
                          pageType="background"
                        />
                      ))
                    )}

                    {/* 添加场景选择面板 */}
                    <ScenePanel
                      selectedScene={selectedScene}
                      onExpandClick={() => setShowSceneSelect(true)}
                      pageType="background"
                    />

                    {/* 随机种子选择器 */}
                    <RandomSeedSelector
                      onRandomChange={setUseRandomSeed}
                      onSeedChange={setSeed}
                      defaultRandom={useRandomSeed}
                      defaultSeed={seed}
                                    // 查看详情时显示历史种子但保持可编辑
              isEdit={false}
              editSeed={selectedImage?.seed || null}
                    />

                    {/* 数量面板 */}
                    <QuantityPanel
                      imageQuantity={imageQuantity}
                      onChange={setImageQuantity}
                    />
          </ControlPanel>

          <ResizeHandle
            ref={handleRef}
            containerRef={containerRef}
            onResize={setControlPanelWidth}
            minWidth={25}
            maxWidth={50}
          />

          <GenerationArea
          ref={generationAreaRef}
            activeTab={activeTab}
            setIsProcessing={setIsProcessing}
            onTabChange={setActiveTab}
            tasks={generationTasks}
            onEditTask={handleEditTask}
            onDownloadImage={handleDownloadImage}
            onViewDetails={handleViewDetails}
            pageType="background"
          />
        </div>

        {/* 上传指南模态框 */}
        {showUploadGuide && (
          <UploadGuideModal
            type={uploadGuideType}
            pageType="background"
            onClose={() => setShowUploadGuide(false)}
            onUpload={(result) => {
              handleForegroundUploadResult(result);
              if (result.shouldClose !== false) {
                setShowUploadGuide(false);
              }
            }}
          />
        )}

        {/* 前景图上传指导弹窗 */}
        {showForegroundUploadGuide && (
          <UploadGuideModal
            type="foreground"
            pageType="background"
            onClose={() => setShowForegroundUploadGuide(false)}
            onUpload={(result) => {
              handleForegroundUploadResult(result);
              setHasUnsavedChanges(true);
              if (result.shouldClose !== false) {
                setShowForegroundUploadGuide(false);
              }
            }}
          />
        )}

        {/* 添加操作弹窗 */}
        {operationsPanel && (
          <ImageInfoModal
            panel={operationsPanel.panel}
            position={operationsPanel.position}
            onClose={() => setOperationsPanel(null)}
            onDelete={handleDeleteForegroundPanel}
            onReupload={handleReuploadForeground}
            pageType="background"
          />
        )}

        {/* 添加 ImageDetailsModal 组件 - 使用懒加载 */}
        {showImageDetails && selectedImage ? (
          <MemoizedImageDetailsModal
            visible={showImageDetails}
            onClose={handleCloseImageDetails}
            selectedImage={selectedImage}
            generationTasks={generationTasks}
            onEditTask={handleEditTask}
            pageType="background"
          />
        ) : null}



        {/* 显示生成中的加载状态 */}
        {isProcessing && (
          <div className="generating-status">
            <Spin size="large" />
            <p>正在生成中，请稍候...</p>
          </div>
        )}

        {/* 添加场景选择弹窗 */}
        {showSceneSelect && (
          <SceneSelectModal
            onClose={() => setShowSceneSelect(false)}
            onSelect={handleSceneSelect}
            selectedSceneId={selectedScene?.id}
            savedSettings={customSceneSettings}
            onSettingsChange={handleCustomSceneSettingsChange}
          />
        )}
      </div>
    </RequireLogin>
  );
};

export default BackgroundPage; 