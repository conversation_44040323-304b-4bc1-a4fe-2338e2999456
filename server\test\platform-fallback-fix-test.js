/**
 * 平台切换修复测试
 * 测试ComfyUI失败后自动切换到RunningHub的完整流程
 */

const path = require('path');
const mongoose = require('mongoose');

// 设置测试环境
process.env.NODE_ENV = 'test';

// 模拟数据库连接
const mockConnect = () => {
  console.log('模拟数据库连接...');
  return Promise.resolve();
};

// 模拟FlowTask模型
const mockFlowTask = {
  findOneAndUpdate: (query, update, options) => {
    console.log('模拟更新任务记录:', {
      query,
      update: {
        platform: update.platform,
        platformInfo: update.platformInfo ? '包含平台切换信息' : '无平台信息'
      }
    });
    return Promise.resolve({
      taskId: query.taskId,
      platform: update.platform,
      status: 'processing'
    });
  }
};

// 模拟UnifiedWorkflowService
class MockUnifiedWorkflowService {
  constructor() {
    this.comfyWorkflowService = {
      executeWorkflow: this.mockComfyUIExecution.bind(this)
    };
  }

  // 模拟ComfyUI执行失败
  async mockComfyUIExecution(workflowName, params, userId, taskId, callback, userContext) {
    console.log('模拟ComfyUI执行...');
    console.log('- 尝试创建ComfyClient');
    console.log('- 健康检查失败');
    
    // 模拟ComfyUI失败
    const error = new Error('创建ComfyClient失败: ComfyUI服务器初始化失败，超过最大重试次数（1）');
    error.statusCode = 500;
    throw error;
  }

  // 模拟平台选择
  async selectPlatform(workflowName, params, context) {
    console.log('模拟平台选择...');
    console.log('- 上下文:', {
      excludeComfyUI: context.excludeComfyUI,
      userId: context.user?.id
    });
    
    if (context.excludeComfyUI) {
      console.log('- 排除ComfyUI，选择RunningHub');
      return 'runninghub';
    } else {
      console.log('- 正常选择，优先ComfyUI');
      return 'comfyui';
    }
  }

  // 模拟RunningHub执行
  async executeOnRunningHub(workflowName, params, userId, taskId, callback, context) {
    console.log('模拟RunningHub执行...');
    console.log('- 查找可用配置');
    console.log('- 创建RunningHub任务');
    console.log('- 执行成功');
    
    return {
      success: true,
      platform: 'runninghub',
      promptId: 'rh_task_123',
      instanceId: '',
      url: 'wss://runninghub.example.com',
      taskId: 'rh_task_123'
    };
  }

  // 模拟ComfyUI执行（简化版，只负责ComfyUI执行）
  async executeOnComfyUI(workflowName, params, userId, taskId, callback, userContext) {
    try {
      console.log('executeOnComfyUI: 开始执行');
      
      // 调用ComfyUI服务
      const result = await this.comfyWorkflowService.executeWorkflow(
        workflowName, params, userId, taskId, callback, userContext
      );
      
      return {
        ...result,
        platform: 'comfyui'
      };
      
    } catch (error) {
      console.log('executeOnComfyUI: 执行失败，释放缓存');
      
      // 释放缓存逻辑
      const instanceIdMatch = error.message.match(/实例[：:\s]*([a-zA-Z0-9\-_]+)/);
      if (instanceIdMatch) {
        console.log(`释放实例 ${instanceIdMatch[1]} 的缓存`);
      }
      
      // 直接抛出错误，让executeWorkflow处理
      throw error;
    }
  }

  // 主要的执行方法（包含平台切换逻辑）
  async executeWorkflow(workflowName, params, userId, taskId, callback, userContext = {}) {
    try {
      console.log('=== executeWorkflow: 开始执行 ===');
      
      // 1. 选择平台
      const context = {
        user: {
          id: userId,
          isVip: userContext.isVip || false,
          isPaid: userContext.isPaid || false,
          ...userContext
        }
      };
      
      const platform = await this.selectPlatform(workflowName, params, context);
      console.log(`选择的平台: ${platform}`);
      
      // 2. 根据平台执行
      if (platform === 'runninghub') {
        return await this.executeOnRunningHub(workflowName, params, userId, taskId, callback, context);
      } else {
        try {
          return await this.executeOnComfyUI(workflowName, params, userId, taskId, callback, userContext);
        } catch (comfyUIError) {
          console.log('=== ComfyUI执行失败，开始平台切换 ===');
          console.log('错误信息:', comfyUIError.message);
          
          // 检查是否是ComfyUI不可用错误
          const isComfyUIUnavailable = comfyUIError.message.includes('ComfyUI服务器初始化失败') ||
                                       comfyUIError.message.includes('创建ComfyClient失败') ||
                                       comfyUIError.message.includes('没有可用的AI实例') ||
                                       comfyUIError.message.includes('服务器健康检查失败') ||
                                       comfyUIError.message.includes('超过最大重试次数');
          
          if (isComfyUIUnavailable) {
            console.log('检测到ComfyUI不可用，开始平台切换...');
            
            // 重新构建上下文，排除ComfyUI
            const fallbackContext = {
              user: {
                id: userId,
                isVip: userContext.isVip || false,
                isPaid: userContext.isPaid || false,
                ...userContext
              },
              excludeComfyUI: true
            };
            
            try {
              // 重新选择平台
              const fallbackPlatform = await this.selectPlatform(workflowName, params, fallbackContext);
              console.log(`切换到平台: ${fallbackPlatform}`);
              
              // 更新任务记录
              await mockFlowTask.findOneAndUpdate(
                { taskId },
                {
                  platform: fallbackPlatform,
                  platformInfo: {
                    fallbackFrom: 'comfyui',
                    fallbackReason: comfyUIError.message,
                    fallbackAt: new Date()
                  }
                },
                { new: true }
              );
              
              if (fallbackPlatform === 'runninghub') {
                console.log('在RunningHub平台执行...');
                return await this.executeOnRunningHub(workflowName, params, userId, taskId, callback, fallbackContext);
              } else {
                throw new Error('没有可用的替代平台');
              }
              
            } catch (fallbackError) {
              console.error('平台切换失败:', fallbackError.message);
              throw new Error(`ComfyUI不可用且平台切换失败: ${fallbackError.message}`);
            }
          } else {
            // 其他类型错误直接抛出
            throw comfyUIError;
          }
        }
      }
      
    } catch (error) {
      console.error('统一工作流执行失败:', error.message);
      throw error;
    }
  }
}

/**
 * 测试平台切换逻辑
 */
async function testPlatformFallback() {
  console.log('开始测试平台切换逻辑...\n');
  
  try {
    const service = new MockUnifiedWorkflowService();
    
    // 测试参数
    const workflowName = 'A01-trending';
    const params = { prompt: 'test prompt' };
    const userId = 'test_user_123';
    const taskId = 'task_456';
    const callback = (promptId, platform, url) => {
      console.log(`回调: promptId=${promptId}, platform=${platform}, url=${url}`);
    };
    const userContext = { isVip: false };
    
    console.log('测试参数:', {
      workflowName,
      userId,
      taskId
    });
    
    // 执行测试
    const result = await service.executeWorkflow(
      workflowName,
      params,
      userId,
      taskId,
      callback,
      userContext
    );
    
    console.log('\n=== 执行结果 ===');
    console.log('成功:', result.success);
    console.log('平台:', result.platform);
    console.log('任务ID:', result.taskId);
    console.log('URL:', result.url);
    
    if (result.success && result.platform === 'runninghub') {
      console.log('✅ 平台切换测试成功！');
      console.log('ComfyUI失败后成功切换到RunningHub平台');
      return true;
    } else {
      console.log('❌ 平台切换测试失败');
      return false;
    }
    
  } catch (error) {
    console.error('测试失败:', error.message);
    return false;
  }
}

/**
 * 运行测试
 */
async function runTest() {
  console.log('平台切换修复测试\n');
  
  const success = await testPlatformFallback();
  
  console.log('\n=== 测试总结 ===');
  if (success) {
    console.log('🎉 测试通过！');
    console.log('修复后的平台切换逻辑工作正常');
  } else {
    console.log('❌ 测试失败');
    console.log('需要进一步检查实现');
  }
  
  console.log('\n=== 关键改进点 ===');
  console.log('1. 移除executeOnComfyUI中的重复平台切换逻辑');
  console.log('2. 在executeWorkflow中统一处理平台切换');
  console.log('3. 使用excludeComfyUI标志避免重复尝试失败平台');
  console.log('4. 保持缓存清理逻辑在executeOnComfyUI中');
  console.log('5. 更新任务记录包含平台切换信息');
  
  return success;
}

// 运行测试
runTest().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('测试运行失败:', error);
  process.exit(1);
});
