import React, { useRef, useEffect, useState, useCallback, Suspense, useMemo } from 'react';
import './index.css';
import { MdOutlineZoomOutMap, MdOutlineAutoAwesome, MdClose, MdOutlineDescription } from 'react-icons/md';
import Masonry from 'masonry-layout';
import { Modal, Button, Spin, message } from 'antd';
import 'antd/dist/reset.css';
import { generateId, ID_TYPES } from '../../../utils/idGenerator';
import UploadGuideModal from '../../../components/UploadGuideModal';
import ImageInfoModal from '../../../components/ImageInfoModal';
import ImageDetailsModal from '../../../components/ImageDetailsModal';
import ControlPanel from '../../../components/ControlPanel';
import ResizeHandle from '../../../components/ResizeHandle';
import GenerationArea from '../../../components/GenerationArea';
import QuantityPanel from '../../../components/QuantityPanel';
import ClothingAttributesPanel from '../../../components/ClothingAttributesPanel';
import ClothingAttributesModal from '../../../components/ClothingAttributesModal';
import RandomSeedSelector from '../../../components/RandomSeedSelector';
import { getCurrentUserId } from '../../../api';
import { showDeleteConfirmModal } from '../../../utils/modalUtils';
import ImageSizeSelector from '../../../components/ImageSizeSelector';
import { handleBatchDownload as downloadHelper } from '../../../utils/downloadHelper';
import RequireLogin from '../../../components/RequireLogin';
import { WORKFLOW_NAME } from '../../../data/workflowName';
import { executeFlow } from '../../../api/flow';
import PromptIfUnsaved from '../../../components/PromptIfUnsaved';
import { useTaskContext } from '../../../contexts/TaskContext';

import { createFlowTask, updateFlowTask, getFlowTasks, getFlowTaskDetail, deleteFlowTask,checkUserBalance } from '../../../api/flowtask';

const MemoizedImageDetailsModal = React.memo(ImageDetailsModal);

const InspirationPage = ({ isLoggedIn, userId }) => {
  const containerRef = useRef(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  // 页面刷新/关闭提示
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）';
        return e.returnValue;
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);
  
  const controlPanelRef = useRef(null);
  const handleRef = useRef(null);
  const [activeTab, setActiveTab] = useState('result');
  const [controlPanelWidth, setControlPanelWidth] = useState(28);
  const showcaseRef = useRef(null);
  const masonryRef = useRef(null);
  const [showUploadGuide, setShowUploadGuide] = useState(false);
  const [processedImages, setProcessedImages] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [operationsPanel, setOperationsPanel] = useState(null);
  const [imageQuantity, setImageQuantity] = useState(2);
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageDetails, setShowImageDetails] = useState(false);
  const [imageDetailsTask, setImageDetailsTask] = useState(null);
  const [showAdvancedText, setShowAdvancedText] = useState(false);

  
  // 添加拖动状态管理
  const [isDragging, setIsDragging] = useState(false);
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const lastPosition = useRef({ x: 0, y: 0 });
  
  // 添加图片缩放相关状态
  const [imageScale, setImageScale] = useState(100);
  const [initialScale, setInitialScale] = useState(100);
  const imageRef = useRef(null);
  
  // 任务列表状态
  const [generationTasks, setGenerationTasks] = useState([]);

  // 初始化和清理Masonry布局
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      // 销毁已存在的实例
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }

      // 创建新实例
      masonryRef.current = new Masonry(showcaseRef.current, {
        itemSelector: '.showcase-item',
        columnWidth: '.showcase-item',
        percentPosition: true,
        transitionDuration: '0.3s',
        initLayout: true,
        gutter: 16,
        fitWidth: false,
        horizontalOrder: true
      });

      // 监听窗口大小变化
      const handleResize = () => {
        if (masonryRef.current) {
          setTimeout(() => {
            masonryRef.current.layout();
          }, 100);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (masonryRef.current) {
          masonryRef.current.destroy();
          masonryRef.current = null;
        }
      };
    }
  }, [activeTab]);

  // 监听图片加载
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      const images = showcaseRef.current.getElementsByTagName('img');
      let loadedCount = 0;

      const handleImageLoad = () => {
        loadedCount++;
        if (masonryRef.current) {
          masonryRef.current.layout();
        }
      };

      Array.from(images).forEach(img => {
        if (img.complete) {
          handleImageLoad();
        } else {
          img.addEventListener('load', handleImageLoad);
        }
      });

      return () => {
        Array.from(images).forEach(img => {
          img.removeEventListener('load', handleImageLoad);
        });
      };
    }
  }, [activeTab]);

  // 处理文件上传
  const handleFileUpload = (file) => {
    setShowUploadGuide(false);
  };

  const handleUploadResult = (results) => {
    console.log('服装上传结果处理:', results);
    
    if (results.type === 'panels') {
      const panelsWithType = results.panels.map(panel => ({
        ...panel,
        type: 'clothing'
      }));
      setProcessedImages(prevImages => [...prevImages, ...panelsWithType]);
    } else if (results.type === 'update') {
      // 处理单个面板更新的情况
      console.log(`更新面板 ${results.panelId} 的状态:`, results.data);
      
      setProcessedImages(prevImages => 
        prevImages.map(panel => {
          if (panel.componentId === results.panelId) {
            // 根据processed数据更新面板
            if (results.data?.processed) {
              const processedData = results.data.processed;
              
              // 构建处理后图片的URL
              let processedImageUrl = null;
              if (processedData.url) {
                // 如果已经提供了构建好的URL，直接使用
                processedImageUrl = processedData.url;
                console.log('使用提供的URL:', processedImageUrl);
              } else if (processedData.relativePath) {
                processedImageUrl = `http://localhost:3002${processedData.relativePath}`;
                console.log('基于相对路径构建URL:', processedImageUrl);
              }
              
              console.log('处理后的图片URL:', processedImageUrl);
              
              // 返回更新后的面板数据
              return {
                ...panel,
                status: 'completed',  // 更新状态为已完成
                processedFile: processedImageUrl,
                fileInfo: processedData.fileInfo || panel.fileInfo,
                error: null
              };
            }
            
            // 如果没有processed数据，检查是否有错误
            if (results.error) {
              return {
                ...panel,
                status: 'error',
                error: results.error
              };
            }
            
            // 如果既没有processed数据也没有错误，保持原样
            return panel;
          }
          
          // 不是要更新的面板，保持不变
          return panel;
        })
      );
    } else if (results.type === 'error') {
      console.error('上传错误:', results.error);
      message.error('上传失败: ' + results.error);
      // 移除处理中的面板
      setProcessedImages(prevImages => 
        prevImages.filter(panel => panel.status !== 'processing'));
    }
  };

  // 处理生成按钮点击
  const handleGenerate = async () => {
        setSeed(useRandomSeed? (Math.floor(Math.random() * Number.MAX_SAFE_INTEGER)) : seed);


    try {
      console.log('开始生成图片');
      
      // 添加服装属性验证
      if (!clothingAttributes) {
        message.error('请设置灵感探索方向');
        return;
      }
      
      setIsProcessing(true);
      
      // 获取当前用户ID
      const currentUserId = userId || getCurrentUserId();
      const balance = await checkUserBalance('灵感探索', 'inspiration', imageQuantity);
      if(balance.code !== 200){
        message.error(balance.message);
setIsProcessing(false);
        return;
      }
      // 创建任务ID
      const taskId = generateId(ID_TYPES.TASK);

      console.log('当前用户ID:', clothingAttributes?.description + clothingAttributes?.mappedDescription || clothingAttributes?.description || '');
      console.log('当前用户ID:',   clothingAttributes?.description || '');
      console.log('当前用户ID:',   clothingAttributes?.attributes?.tags || ['自定义']);
      console.log('当前用户ID:',   clothingAttributes?.attributes?.mappedTags || []);
      console.log('当前用户ID:',   {
        type: 'custom',
        description: clothingAttributes?.description || '',
        mappedDescription: clothingAttributes?.mappedDescription || '',
        tags: clothingAttributes?.attributes?.tags || ['自定义'],
        mappedTags: clothingAttributes?.attributes?.mappedTags || []
      });
      // 获取图片尺寸设置
      let sizeSetting = {};
      if (!useDefaultSize) {
        // 使用自定义尺寸
        sizeSetting = {
          width: imageWidth,
          height: imageHeight
        };
      }
      
      // 准备提交数据
      const taskData = {
        taskId: taskId,
        id: taskId,
        userId: currentUserId,
        createdAt: new Date(),
        status: 'processing',
        imageCount: imageQuantity,
        taskType: 'inspiration', // 指定任务类型为灵感探索
        pageType: 'inspiration', // 指定页面类型
        // 使用组件数组结构
        components: [
          {
            componentType: 'clothingAttributesPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '服装属性',
            prompt: clothingAttributes?.mappedDescription || clothingAttributes?.description || '',
            attributes: {
              type: 'custom',
              description: clothingAttributes?.description || '',
              mappedDescription: clothingAttributes?.mappedDescription || '',
              tags: clothingAttributes?.attributes?.tags || ['自定义'],
              mappedTags: clothingAttributes?.attributes?.mappedTags || [],
              ...clothingAttributes.attributes
            },
            ...clothingAttributes
          },
          {
            componentType: 'randomSeedSelector',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '随机种子',
            useRandom: false,
            value: seed
          },
          {
            componentType: 'imageSizeSelector',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '图片尺寸',
            useDefault: false,
            width: imageWidth,
            height: imageHeight
          },
          {
            componentType: 'quantityPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '生成数量',
            quantity: imageQuantity
          }
        ],
        // 初始状态下的空图像数组
        generatedImages: Array(imageQuantity).fill(null).map((_, index) => ({
          imageIndex: index,
          status: 'processing'
        })),
        processInfo:{
          results:[]
        }
      };
      if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }

      // 先添加到本地状态，使UI立即响应
      try {
        // 创建工作流任务
        await createFlowTask(taskData);
      
        // 执行工作流
        const {taskId,promptId,instanceId,status,url} = await executeFlow(WORKFLOW_NAME.INSPIRATION,{
          "11":{
             "width": imageWidth,
             "height": imageHeight,
             "batch_size":imageQuantity
          },
          "5":{
            "seed": seed
          },
          "14":{
            "text": clothingAttributes?.attributes.mappedDescription 
          },
          "17":{
            "String":(clothingAttributes?.attributes?.tags || ['自定义']).join(',')
          },
          "subInfo":{
            "type": "inspiration",
            "title":"灵感探索",
            "count":imageQuantity
          }
        },taskData.taskId);
        setIsProcessing(false);
        setHasUnsavedChanges(false);

        if( generationAreaRef.current){
          taskData.promptId = promptId;
          taskData.instanceId = instanceId;
          taskData.url = url;
          taskData.newTask = true;
          generationAreaRef.current.setGenerationTasks(taskData);
      }
        // 更新任务列表
      } catch (error) {
        setIsProcessing(false);
        setHasUnsavedChanges(false);
        // 更新任务状态为失败
        taskData.status = 'failed';
        taskData.errorMessage = error.message;
        if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
        
        // 调用updateTask以触发失败提示音
        updateTask(taskData);
        
        // 更新任务列表
      }   
      console.log('提交的任务数据:', taskData);

      // 切换到结果标签页
      setActiveTab('result');
     
    } catch (error) {
      setIsProcessing(false);
      setHasUnsavedChanges(false);
      console.error('生成过程中出错:', error);
      message.error('灵感探索生成失败: ' + (error.message || '未知错误'));
      setIsProcessing(false);
    }   
  };

  // 处理单张图片下载
  const handleDownloadImage = async (imageUrl, taskId, index) => {
    try {
      message.info('准备下载...');
      const httpsUrl = imageUrl.replace(/^http:/, 'https:');
      const response = await fetch(httpsUrl);
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `${taskId}_${parseInt(index) + 1}.jpg`;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl);
    } catch (error) {
      message.error('下载失败');
      console.error('下载出错:', error);
    }
  };

  // 处理编辑任务
  const handleEditTask = (task) => {
    setHasUnsavedChanges(true);
    if (!task) {
      message.warning('无法编辑：未找到任务信息');
      return;
    }

    // 设置任务ID
    setCurrentTaskId(task.taskId);
    
    // 只处理数组结构，不再兼容对象结构
    const components = Array.isArray(task.components) ? task.components : [];
    console.log('处理的组件数据:', components);
    
    // 回填服装属性设置
    const clothingAttributesPanel = components.find(c => c.componentType === 'clothingAttributesPanel');
    if (clothingAttributesPanel) {
      console.log('获取到服装属性组件:', clothingAttributesPanel);
      setClothingAttributes({...clothingAttributesPanel.attributes,...clothingAttributesPanel} || {});
      setPrompt(clothingAttributesPanel.prompt || '');
    }
    
    // 回填尺寸设置
    const imageSizeSelector = components.find(c => c.componentType === 'imageSizeSelector');
    if (imageSizeSelector) {
      console.log('获取到尺寸选择器组件:', imageSizeSelector);
      setUseDefaultSize(imageSizeSelector.useDefault);
      if (!imageSizeSelector.useDefault) {
        setImageWidth(imageSizeSelector.width);
        setImageHeight(imageSizeSelector.height);
      }
    }
    
    // 回填种子设置
    const seedComponent = components.find(c => c.componentType === 'randomSeedSelector');
    if (seedComponent) {
      console.log('获取到种子组件:', seedComponent);
      setUseRandomSeed(seedComponent.useRandom);
      if (!seedComponent.useRandom) {
        setSeed(seedComponent.value);
      }
    }
    
    // 回填数量设置
    const quantityPanel = components.find(c => c.componentType === 'quantityPanel');
    if (quantityPanel) {
      console.log('获取到数量面板组件:', quantityPanel);
      setImageQuantity(quantityPanel.quantity);
    }
    
    // 切换到结果标签页
    setActiveTab('result');
    
    // 显示成功消息
    message.success({
      content: '配置已重新导入，可继续进行调整',
      duration: 3
    });
  };

  // 添加点击外部关闭下拉菜单的处理
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdowns = document.querySelectorAll('.dropdown-menu');
      dropdowns.forEach(dropdown => {
        if (!dropdown.parentElement.contains(event.target)) {
          dropdown.classList.remove('show');
        }
      });
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const handleViewDetails = (image, task) => {
    console.log('查看详情, 完整任务数据:', task);
    console.log('查看详情, 图片数据:', image);
    
    // 预加载生成图
    if (image.url) {
      const resultImg = new Image();
      resultImg.src = image.url;
      console.log('预加载结果图:', image.url);
    }
    
    // 只处理数组结构，不再兼容对象结构
    const components = Array.isArray(task.components) ? task.components : [];
    console.log('查看详情 - 处理的组件数据:', components);
    
    // 使用components.find获取组件
    const randomSeedSelector = components.find(c => c.componentType === 'randomSeedSelector');
    const clothingAttributesPanel = components.find(c => c.componentType === 'clothingAttributesPanel');
    const imageSizeSelector = components.find(c => c.componentType === 'imageSizeSelector');
    const quantityPanel = components.find(c => c.componentType === 'quantityPanel');
    
    // 记录组件获取结果
    console.log('查看详情, 组件获取结果:', {
      randomSeedSelector,
      clothingAttributesPanel,
      imageSizeSelector,
      quantityPanel
    });
    
    // 获取种子值 - 按照标准优先级顺序
    const taskSeed = task.seed !== undefined && task.seed >= 0 ? task.seed : 
                   (randomSeedSelector?.value !== undefined && randomSeedSelector.value >= 0 ? 
                    randomSeedSelector.value : (image.seed || 0));
    
    console.log('查看详情, 使用种子值:', taskSeed);
    
    // 准备适合ImageDetailsModal的组件数据结构 - 使用数组格式
    const adaptedComponents = [
      {
        componentType: 'clothingAttributesPanel',
        componentId: generateId(ID_TYPES.COMPONENT),
        id: generateId(ID_TYPES.COMPONENT),
        name: '服装属性',
        prompt: clothingAttributesPanel?.prompt || '',
        negativePrompt: clothingAttributesPanel?.negativePrompt || '',
        attributes: clothingAttributesPanel?.attributes || {
          type: 'custom',
          description: '',
          negativeDescription: '',
          tags: []
        }
      },
      {
        componentType: 'randomSeedSelector',
        componentId: generateId(ID_TYPES.COMPONENT),
        id: generateId(ID_TYPES.COMPONENT),
        name: '随机种子',
        useRandom: randomSeedSelector?.useRandom || false,
        value: taskSeed
      },
      {
        componentType: 'imageSizeSelector',
        componentId: generateId(ID_TYPES.COMPONENT),
        id: generateId(ID_TYPES.COMPONENT),
        name: '图片尺寸',
        useDefault: imageSizeSelector?.useDefault || true,
        width: imageSizeSelector?.width || 1024,
        height: imageSizeSelector?.height || 1536
      },
      {
        componentType: 'quantityPanel',
        componentId: generateId(ID_TYPES.COMPONENT),
        id: generateId(ID_TYPES.COMPONENT),
        name: '生成数量',
        quantity: quantityPanel?.quantity || 2
      }
    ];
    
    // 添加选中图片的任务信息
    const enhancedImage = {
      ...image,
      // 确保使用componentId
      componentId: generateId(ID_TYPES.COMPONENT),
      taskId: task.taskId,
      generatedId: image.generatedId || image.imageIndex,
      // 添加种子值
      seed: image.seed !== undefined ? image.seed : taskSeed,
      // 使用标准的组件列表
      components: adaptedComponents
    };
    
    return enhancedImage
  };

  // 添加关闭弹窗时的处理函数
  const handleCloseImageDetails = () => {
    // 直接关闭弹窗，不使用动画
    setShowImageDetails(false);
    
    // 重置状态，无需延迟
    setSelectedImage(null);
    // 重置任务信息，避免保留旧任务导致重新打开时出错
    setImageDetailsTask(null);
    setImagePosition({ x: 0, y: 0 });
    setIsDragging(false);
    lastPosition.current = { x: 0, y: 0 };
    setImageScale(100);
    setInitialScale(100);
    

    
    // 重置文本弹窗状态
    setShowAdvancedText(false);
  };

  // 添加点击外部关闭弹出层的处理函数
  useEffect(() => {
    const handleClickOutside = (event) => {
      // 检查点击是否发生在文本按钮上
      const isTextButton = event.target.closest('.text-button');
      // 检查点击是否发生在弹窗内部
      const isInsidePopup = event.target.closest('.text-popup');
      
      // 如果点击既不是文本按钮也不是弹窗内部，则关闭所有弹窗
      if (!isTextButton && !isInsidePopup) {
        setShowAdvancedText(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 计算初始缩放比例
  const calculateInitialScale = (img) => {
    if (!img) return 100;
    const container = img.parentElement;
    if (!container) return 100;
    
    // 计算图片在容器中的实际显示尺寸与真实尺寸的比例
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const imageNaturalWidth = img.naturalWidth;
    const imageNaturalHeight = img.naturalHeight;
    
    // 计算图片适应容器时的尺寸
    const containerRatio = containerWidth / containerHeight;
    const imageRatio = imageNaturalWidth / imageNaturalHeight;
    
    let scale;
    if (imageRatio > containerRatio) {
      // 图片较宽，以容器宽度为基准
      scale = (containerWidth / imageNaturalWidth) * 100;
    } else {
      // 图片较高，以容器高度为基准
      scale = (containerHeight / imageNaturalHeight) * 100;
    }

    // 如果计算出的缩放比例大于100%，说明图片实际尺寸小于容器
    // 这种情况下我们应该将图片显示为其实际大小
    if (scale > 100) {
      scale = 100;
    }
    
    // 返回相对于真实尺寸的缩放百分比，四舍五入到整数
    return Math.round(scale);
  };

  // 处理图片加载完成
  const handleImageLoad = (e) => {
    const img = e.target;
    const initialScaleValue = calculateInitialScale(img);
    setInitialScale(initialScaleValue);  // 设置初始比例
    setImageScale(initialScaleValue);    // 设置当前比例
    imageRef.current = img;
  };


  const [currentTaskId, setCurrentTaskId] = useState(null);

  // 处理打开蒙版绘制弹窗 - 保留空函数架构避免报错
  const handleDrawMask = (panel) => {
    // 功能已移除
    console.log('蒙版功能不适用于灵感探索页面');
    message.info('灵感探索功能不支持蒙版绘制');
  };

  // 添加一个ref用于保存GenerationArea组件实例
  const generationAreaRef = useRef(null);

  // 添加服装属性相关的状态
  const [showClothingAttributesModal, setShowClothingAttributesModal] = useState(false);
  const [clothingAttributes, setClothingAttributes] = useState(null);
  const [prompt, setPrompt] = useState('');

  // 处理服装属性面板点击
  const handleClothingAttributesPanelClick = () => {
    setShowClothingAttributesModal(true);
  };
  
  // 处理服装属性设置变更
  const handleClothingAttributesChange = (settings) => {
    setHasUnsavedChanges(true);
    setClothingAttributes(settings);
  };
  
  // 处理服装属性弹窗关闭
  const handleClothingAttributesModalClose = () => {
    setShowClothingAttributesModal(false);
  };

  // 添加随机种子相关的状态
  const [seed, setSeed] = useState(Math.floor(Math.random() * Number.MAX_SAFE_INTEGER));
  const [useRandomSeed, setUseRandomSeed] = useState(true);
  
  // 添加图片尺寸相关状态
  const [useDefaultSize, setUseDefaultSize] = useState(true);  // 是否使用默认尺寸
  const [imageWidth, setImageWidth] = useState(1024);  // 图片宽度
  const [imageHeight, setImageHeight] = useState(1536);  // 图片高度
  
  // 添加更新默认尺寸的钩子 - 根据选择的模型图片更新
  useEffect(() => {
    if (operationsPanel && operationsPanel.fileInfo) {
      const panel = operationsPanel;
      if (panel.fileInfo.width && panel.fileInfo.height) {
        setImageWidth(panel.fileInfo.width);
        setImageHeight(panel.fileInfo.height);
      }
    }
  }, [operationsPanel]);

  const { updateTask } = useTaskContext();

  return (
    <RequireLogin isLoggedIn={isLoggedIn} featureName="灵感探索功能">
      <PromptIfUnsaved when={hasUnsavedChanges} message="操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）" />
      <div className="inspiration-page">
        <div className="inspiration-container" ref={containerRef}>
          <ControlPanel
            ref={controlPanelRef}
            width={`${controlPanelWidth}%`}
            onGenerate={handleGenerate}
            disabled={isProcessing}
            featureName="inspiration"
            quantity={imageQuantity}
          >
            {/* 控制面板内容 */}
            
            {/* 服装属性面板 */}
            <ClothingAttributesPanel 
              onExpandClick={handleClothingAttributesPanelClick}
              clothingAttributes={clothingAttributes}
            />
            
            {/* 随机种子选择器 */}
            <RandomSeedSelector
              onRandomChange={setUseRandomSeed}
              onSeedChange={setSeed}
              defaultRandom={useRandomSeed}
              defaultSeed={seed}
              // 编辑模式下传递历史种子
              isEdit={selectedImage !== null}
              editSeed={selectedImage?.seed || null}
            />
            
            {/* 图片尺寸选择器 */}
            <ImageSizeSelector
              onUseDefaultChange={setUseDefaultSize}
              onWidthChange={setImageWidth}
              onHeightChange={setImageHeight}
              defaultUseDefault={useDefaultSize}
              defaultWidth={imageWidth}
              defaultHeight={imageHeight}
              pageType="inspiration"
              imageData={{
                uploadedImages: operationsPanel ? [operationsPanel] : []
              }}
            />
            
            {/* 数量生成面板 */}
            <QuantityPanel 
              imageQuantity={imageQuantity} 
              onChange={setImageQuantity} 
            />
          </ControlPanel>

          <ResizeHandle
            ref={handleRef}
            containerRef={containerRef}
            onResize={setControlPanelWidth}
            minWidth={25}
            maxWidth={50}
          />

                  <GenerationArea
            ref={generationAreaRef}    setIsProcessing={setIsProcessing}

            activeTab={activeTab}
            onTabChange={setActiveTab}
            tasks={Array.isArray(generationTasks) ? generationTasks : []}
            onEditTask={handleEditTask}
            onViewDetails={handleViewDetails}
            pageType="inspiration"
          />
        </div>

        {/* 上传指导弹窗 */}
        {showUploadGuide && (
          <UploadGuideModal
            type="inspiration"
            pageType="inspiration"
            onClose={() => setShowUploadGuide(false)}
            onUpload={(result) => {
              console.log('收到上传结果:', result);
              handleUploadResult(result);
              
              // 根据结果中的shouldClose字段决定是否关闭弹窗
              if (result.shouldClose !== false) {
                setShowUploadGuide(false);
              }
            }}
          />
        )}

        {/* 添加操作弹窗 */}
        {operationsPanel && (
          <ImageInfoModal
            panel={operationsPanel.panel}
            position={operationsPanel.position}
            onClose={() => setOperationsPanel(null)}
            onDelete={() => console.log('删除功能已禁用')}
            onReupload={() => console.log('重新上传功能已禁用')}
            onDrawMask={handleDrawMask}
            pageType="inspiration"
          />
        )}

        {/* 添加 ImageDetailsModal 组件 - 使用懒加载 */}
        {showImageDetails && selectedImage ? (
          <MemoizedImageDetailsModal
            visible={showImageDetails}
            onClose={handleCloseImageDetails}
            selectedImage={selectedImage}
            generationTasks={generationTasks}
            onEditTask={handleEditTask}
            pageType="inspiration"
          />
        ) : null}

        {/* 服装属性设置弹窗 */}
        {showClothingAttributesModal && (
          <ClothingAttributesModal
            onClose={handleClothingAttributesModalClose}
            onSelect={handleClothingAttributesChange}
            savedSettings={clothingAttributes}
            onSettingsChange={handleClothingAttributesChange}
            pageType="inspiration"
          />
        )}



        {/* 显示生成中的加载状态 */}
        {isProcessing && (
          <div className="generating-status">
            <Spin size="large" />
            <p>正在生成中，请稍候...</p>
          </div>
        )}
        
        {/* 显示生成的图片 */}
        {processedImages.length > 0 && (
          <div className="generated-images">
            <h3>生成结果</h3>
            <div className="image-grid">
              {processedImages.map((image, index) => (
                <div key={index} className="image-item">
                  <img src={image.url} alt={`生成图片 ${index + 1}`} />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </RequireLogin>
  );
};

export default InspirationPage; 